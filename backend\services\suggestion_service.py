from typing import Dict, Any, List
from ..core.logging import logger
from ..ml.base_model import BaseModel
from .data_service import DataService

class SuggestionService:
    """
    Dịch vụ gợi ý cây trồng
    """
    def __init__(self, model: BaseModel, data_service: DataService):
        """
        Khởi tạo dịch vụ gợi ý

        Args:
            model: <PERSON><PERSON> hình học máy
            data_service: Dịch vụ dữ liệu
        """
        self.model = model
        self.data_service = data_service

    def get_suggestions(self, input_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Lấy gợi ý cây trồng dựa trên dữ liệu đầu vào

        Args:
            input_data: Dữ liệu đầu vào

        Returns:
            Danh sách các cây trồng được gợi ý
        """
        logger.info("Đang lấy gợi ý cây trồng")

        # D<PERSON> đoán sử dụng mô hình
        prediction_results = self.model.predict(input_data)

        # Xử lý kết quả dự đoán
        if hasattr(self.model, 'expected_columns'):
            # Đ<PERSON><PERSON> là mô hình Random Forest, kết quả đã bao gồm thông tin cơ bản
            # Bổ sung thông tin chi tiết
            for result in prediction_results:
                crop_name = result["name"]
                details = self.data_service.get_crop_details_by_name(crop_name)
                result["details"] = details

            return prediction_results
        else:
            # Đây là mô hình KNN, kết quả là danh sách các ID cây trồng
            # Lấy thông tin chi tiết cho từng cây trồng
            crop_ids = prediction_results
            suggestions = [self.data_service.get_crop_info(crop_id) for crop_id in crop_ids]

            return suggestions
