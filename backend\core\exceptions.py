from fastapi import HTTPException, status

class ModelNotFoundException(Exception):
    """Exception khi không tìm thấy model"""
    pass

class DataNotFoundException(Exception):
    """Exception khi không tìm thấy dữ liệu"""
    pass

class InvalidInputException(Exception):
    """Exception khi dữ liệu đầu vào không hợp lệ"""
    pass

class PredictionException(Exception):
    """Exception khi có lỗi trong quá trình dự đoán"""
    pass

# Hàm chuyển đổi exception thành HTTP exception
def convert_exception_to_http_exception(exc: Exception) -> HTTPException:
    """
    Chuyển đổi exception thành HTTP exception
    """
    if isinstance(exc, ModelNotFoundException):
        return HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Không tìm thấy model: {str(exc)}"
        )
    elif isinstance(exc, DataNotFoundException):
        return HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Không tìm thấy dữ liệu: {str(exc)}"
        )
    elif isinstance(exc, InvalidInputException):
        return HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Dữ liệu đầu vào không hợp lệ: {str(exc)}"
        )
    elif isinstance(exc, PredictionException):
        return HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Lỗi khi dự đoán: {str(exc)}"
        )
    else:
        return HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Lỗi không xác định: {str(exc)}"
        )
