import pandas as pd
from typing import Dict, Any, Optional

def safe_get(row: Dict[str, Any], column: str, default: Any = None) -> Any:
    """
    Lấy giá trị an toàn từ một hàng dữ liệu
    
    Args:
        row: <PERSON>àng dữ liệu
        column: Tên cột
        default: <PERSON>i<PERSON> trị mặc định
    
    Returns:
        Gi<PERSON> trị của cột hoặc giá trị mặc định nếu không tồn tại hoặc là NaN
    """
    value = row.get(column, default)
    # Kiểm tra nếu giá trị là NaN (pd.isna) và trả về None
    if pd.isna(value):
        return default
    return value

def safe_int(value: Any, default: int = 0) -> int:
    """
    Chuyển đổi giá trị thành số nguyên an toàn
    
    Args:
        value: Giá trị cần chuyển đổi
        default: Gi<PERSON> trị mặc định
    
    Returns:
        Gi<PERSON> trị số nguyên hoặc giá trị mặc định nếu không thể chuyển đổi
    """
    try:
        if pd.isna(value):
            return default
        return int(value)
    except (ValueError, TypeError):
        return default

def safe_float(value: Any, default: float = 0.0) -> float:
    """
    Chuyển đổi giá trị thành số thực an toàn
    
    Args:
        value: Giá trị cần chuyển đổi
        default: Giá trị mặc định
    
    Returns:
        Giá trị số thực hoặc giá trị mặc định nếu không thể chuyển đổi
    """
    try:
        if pd.isna(value):
            return default
        return float(value)
    except (ValueError, TypeError):
        return default
