import pandas as pd
from typing import Dict, Any, Optional, List
from ..core.logging import logger
from ..core.exceptions import DataNotFoundException

class DataService:
    """
    Dịch vụ xử lý dữ liệu cây trồng
    """
    def __init__(self, species_data_path: str):
        """
        Khởi tạo dịch vụ dữ liệu

        Args:
            species_data_path: Đường dẫn đến file dữ liệu species
        """
        self.species_data_path = species_data_path
        self.species_data = self._load_species_data()

    def _load_species_data(self) -> pd.DataFrame:
        """
        Tải dữ liệu species từ file

        Returns:
            DataFrame chứa dữ liệu species

        Raises:
            DataNotFoundException: Nếu không tìm thấy dữ liệu
        """
        try:
            logger.info(f"Đang tải dữ liệu species từ {self.species_data_path}")
            data = pd.read_csv(self.species_data_path)
            logger.info(f"Đ<PERSON> tải dữ liệu species thành công với {len(data)} bản ghi")
            return data
        except Exception as e:
            logger.error(f"Lỗi khi tải dữ liệu species: {str(e)}")
            raise DataNotFoundException(f"Không thể tải dữ liệu species từ {self.species_data_path}: {str(e)}")

    def _format_growing_cycle(self, min_cycle, max_cycle):
        """
        Định dạng chu kỳ phát triển

        Args:
            min_cycle: Chu kỳ tối thiểu
            max_cycle: Chu kỳ tối đa

        Returns:
            Chuỗi mô tả chu kỳ phát triển
        """
        if pd.isna(min_cycle) and pd.isna(max_cycle):
            return None
        if pd.isna(min_cycle):
            return f"Up to {max_cycle} days"
        if pd.isna(max_cycle):
            return f"At least {min_cycle} days"
        if min_cycle == max_cycle:
            return f"{min_cycle} days"
        return f"{min_cycle}-{max_cycle} days"

    def get_crop_info(self, crop_id: int) -> Dict[str, Any]:
        """
        Lấy thông tin chi tiết về cây trồng

        Args:
            crop_id: ID của cây trồng

        Returns:
            Thông tin chi tiết về cây trồng

        Raises:
            DataNotFoundException: Nếu không tìm thấy cây trồng
        """
        try:
            # Tìm kiếm cây trồng theo ID
            crop_data = self.species_data[self.species_data['species_code'] == crop_id]

            if crop_data.empty:
                raise DataNotFoundException(f"Không tìm thấy cây trồng với ID {crop_id}")

            # Lấy dòng đầu tiên
            crop_data = crop_data.iloc[0]

            # Xử lý các giá trị NaN
            def safe_value(value):
                return None if pd.isna(value) else value

            # Đối với các trường số nguyên, cần chuyển đổi thành int nếu không phải NaN
            def safe_int(value, default=0):
                return default if pd.isna(value) else int(value)

            # Định dạng chu kỳ phát triển
            growing_cycle = self._format_growing_cycle(
                safe_value(crop_data.get('cycle_min')),
                safe_value(crop_data.get('cycle_max'))
            )

            # Tạo đối tượng thông tin cây trồng
            crop_info = {
                "id": crop_id,
                "species_name": safe_value(crop_data.get('species')) or "Unknown",
                "life_form": safe_value(crop_data.get('Life.form')) or "Unknown",
                "habit": safe_value(crop_data.get('Habit')) or "Unknown",
                "life_span": safe_value(crop_data.get('Life.span')) or "Unknown",
                "physiology": safe_value(crop_data.get('Physiology')),
                "category": safe_value(crop_data.get('Category')) or "Unknown",
                "plant_attributes": safe_value(crop_data.get('Plant.attributes')),
                "growing_cycle": growing_cycle,
                "main_use": safe_value(crop_data.get('use.main')) or "Unknown",
                "detailed_use": safe_value(crop_data.get('use.detailed')),
                "used_part": safe_value(crop_data.get('use.part')) or "Unknown",
                "min_growing_cycle": safe_int(crop_data.get('cycle_min')),
                "max_growing_cycle": safe_int(crop_data.get('cycle_max'))
            }

            return crop_info

        except DataNotFoundException:
            raise
        except Exception as e:
            logger.error(f"Lỗi khi lấy thông tin cây trồng: {str(e)}")
            raise DataNotFoundException(f"Lỗi khi lấy thông tin cây trồng với ID {crop_id}: {str(e)}")

    def get_crop_details_by_name(self, crop_name: str) -> Dict[str, Any]:
        """
        Lấy thông tin chi tiết về cây trồng theo tên

        Args:
            crop_name: Tên của cây trồng

        Returns:
            Thông tin chi tiết về cây trồng

        Raises:
            DataNotFoundException: Nếu không tìm thấy cây trồng
        """
        try:
            # Ánh xạ tên cây trồng đơn giản sang tên khoa học
            crop_mapping = {
                "rice": "Oryza sativa",
                "maize": "Zea mays",
                "chickpea": "Cicer arietinum",
                "kidneybeans": "Phaseolus vulgaris",
                "pigeonpeas": "Cajanus cajan",
                "mothbeans": "Vigna aconitifolia",
                "mungbean": "Vigna radiata",
                "blackgram": "Vigna mungo",
                "lentil": "Lens culinaris",
                "pomegranate": "Punica granatum",
                "banana": "Musa acuminata",
                "mango": "Mangifera indica",
                "grapes": "Vitis vinifera",
                "watermelon": "Citrullus lanatus",
                "muskmelon": "Cucumis melo",
                "apple": "Malus domestica",
                "orange": "Citrus sinensis",
                "papaya": "Carica papaya",
                "coconut": "Cocos nucifera",
                "cotton": "Gossypium hirsutum",
                "jute": "Corchorus capsularis",
                "coffee": "Coffea arabica"
            }

            # Chuẩn hóa tên cây trồng
            normalized_name = crop_name.lower().strip()

            # Tìm tên khoa học tương ứng
            scientific_name = crop_mapping.get(normalized_name)

            # Nếu không tìm thấy trong ánh xạ, thử tìm trực tiếp
            if scientific_name:
                crop_info = self.species_data[self.species_data['species'].str.lower() == scientific_name.lower()]
            else:
                # Tìm kiếm cây trồng theo tên thông thường trong cột species
                crop_info = self.species_data[self.species_data['species'].str.lower().str.contains(normalized_name)]

                # Nếu không tìm thấy, thử tìm trong tên tiếng Anh thông thường
                if crop_info.empty:
                    # Tạo một danh sách các tên tiếng Anh thông thường từ tên khoa học
                    common_names = [name.split()[-1].lower() for name in self.species_data['species']]
                    matches = [i for i, name in enumerate(common_names) if normalized_name in name]

                    if matches:
                        crop_info = self.species_data.iloc[matches[0:1]]

            # Nếu vẫn không tìm thấy, trả về thông tin cơ bản
            if crop_info.empty:
                # Chuẩn hóa tên hiển thị
                display_name = normalized_name.capitalize()

                # Thông tin mặc định cho các loại cây trồng phổ biến
                default_info = {
                    "rice": {
                        "life_form": "Grass",
                        "habit": "Erect",
                        "life_span": "Annual",
                        "category": "Cereal",
                        "main_use": "Food",
                        "detailed_use": "Staple food",
                        "used_part": "Seed",
                        "growing_cycle": "90-120 days"
                    },
                    "maize": {
                        "life_form": "Grass",
                        "habit": "Erect",
                        "life_span": "Annual",
                        "category": "Cereal",
                        "main_use": "Food",
                        "detailed_use": "Staple food",
                        "used_part": "Seed",
                        "growing_cycle": "80-110 days"
                    }
                }

                # Lấy thông tin mặc định nếu có
                info = default_info.get(normalized_name, {})

                return {
                    "name": display_name,
                    "description": f"Thông tin về {display_name}",
                    **info
                }

            # Lấy dòng đầu tiên
            row = crop_info.iloc[0]

            # Chuẩn hóa tên hiển thị
            display_name = normalized_name.capitalize()
            if scientific_name:
                display_name = f"{display_name} ({scientific_name})"

            # Tạo đối tượng thông tin chi tiết
            details = {
                "name": display_name,
                "description": f"Thông tin về {display_name}"
            }

            # Ánh xạ tên cột sang tên hiển thị thân thiện hơn
            column_mapping = {
                'Life.form': 'life_form',
                'Habit': 'habit',
                'Life.span': 'life_span',
                'Physiology': 'physiology',
                'Category': 'category',
                'Plant.attributes': 'plant_attributes',
                'cycle_min': 'min_growing_cycle',
                'cycle_max': 'max_growing_cycle',
                'use.main': 'main_use',
                'use.detailed': 'detailed_use',
                'use.part': 'used_part'
            }

            # Thêm các thông tin khác
            for col in crop_info.columns:
                if col != 'species' and col != 'species_code' and not pd.isna(row[col]):
                    # Sử dụng tên cột đã ánh xạ nếu có
                    key = column_mapping.get(col, col)
                    details[key] = row[col]

            # Thêm thông tin chu kỳ phát triển
            if 'min_growing_cycle' in details and 'max_growing_cycle' in details:
                min_cycle = details.get('min_growing_cycle')
                max_cycle = details.get('max_growing_cycle')
                details['growing_cycle'] = self._format_growing_cycle(min_cycle, max_cycle)

            return details

        except Exception as e:
            logger.warning(f"Không thể lấy thông tin chi tiết cho {crop_name}: {str(e)}")
            # Chuẩn hóa tên hiển thị
            display_name = crop_name.lower().strip().capitalize()

            return {
                "name": display_name,
                "description": f"Thông tin về {display_name}"
            }
