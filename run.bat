@echo off
echo Khởi động hệ thống gợi ý cây trồng...

REM Kiểm tra môi trường ảo Python
if not exist .venv (
    echo Tạo môi trường ảo Python...
    python -m venv .venv
)

REM Kích hoạt môi trường ảo
call .venv\Scripts\activate

REM Cài đặt các thư viện Python
echo Cài đặt các thư viện Python...
pip install -r requirements.txt

REM Khởi động backend trong cửa sổ mới
echo Khởi động backend...
start cmd /k "call .venv\Scripts\activate && uvicorn backend.main:app --reload"

REM Đợi backend khởi động
timeout /t 5

REM Di chuyển vào thư mục frontend
cd frontend

REM Cài đặt các thư viện Node.js
echo Cài đặt các thư viện Node.js...
call npm install

REM Khởi động frontend
echo Khởi động frontend...
start cmd /k "npm start"

REM Mở trình duyệt
timeout /t 5
start http://localhost:3000

echo Hệ thống đã khởi động. Đóng các cửa sổ terminal để dừng.
