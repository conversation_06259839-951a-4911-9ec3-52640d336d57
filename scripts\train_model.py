import pandas as pd
import joblib
from sklearn.preprocessing import StandardScaler
from sklearn.neighbors import NearestNeighbors
import os
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Define absolute paths
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
MODEL_DIR = os.path.join(BASE_DIR, 'model')
DATA_FILE = os.path.join(BASE_DIR, 'data', 'crop_env_features_no_abs - crop_env_features_no_abs.csv')

logger.info(f"BASE_DIR: {BASE_DIR}")
logger.info(f"DATA_FILE: {DATA_FILE}")

try:
    # Create model directory if it doesn't exist
    os.makedirs(MODEL_DIR, exist_ok=True)
    logger.info(f"Created/verified model directory: {MODEL_DIR}")

    # Load data
    logger.info("Loading dataset...")
    df = pd.read_csv(DATA_FILE, encoding='utf-8')
    logger.info(f"Loaded {len(df)} rows of data")

    # Check data
    logger.info(f"Columns in dataset: {df.columns.tolist()}")
    
    X = df.drop(columns=['species_label'])
    labels = df['species_label'].values
    logger.info(f"Feature matrix shape: {X.shape}")

    # Fit scaler
    logger.info("Fitting scaler...")
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    logger.info("Scaler fitted successfully")

    # Fit KNN model
    logger.info("Training KNN model...")
    nn = NearestNeighbors(n_neighbors=5, metric='euclidean')
    nn.fit(X_scaled)
    logger.info("KNN model trained successfully")

    # Save artifacts
    logger.info("Saving models...")
    joblib.dump(scaler, os.path.join(MODEL_DIR, 'scaler.joblib'))
    joblib.dump(nn, os.path.join(MODEL_DIR, 'nn_model.joblib'))
    joblib.dump(labels, os.path.join(MODEL_DIR, 'labels.joblib'))
    logger.info('Models saved successfully!')

except Exception as e:
    logger.error(f"An error occurred: {str(e)}")
    raise
