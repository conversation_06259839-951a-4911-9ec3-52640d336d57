from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional

class CropRequest(BaseModel):
    """
    Model cho request gợi ý cây trồng sử dụng KNN
    """
    # Các trường số cơ bản
    temp_opt_min: float    # Nhiệt độ tối thiểu tối ưu
    temp_opt_max: float    # Nhiệt độ tối đa tối ưu
    rain_opt_min: float    # Lượng mưa tối thiểu tối ưu
    rain_opt_max: float    # Lượng mưa tối đa tối ưu
    ph_opt_min: float      # pH tối thiểu tối ưu
    ph_opt_max: float      # pH tối đa tối ưu

    # Các trường phân loại dạng số nguyên
    depth_opt: int         # Độ sâu đất tối ưu
    fertility_ops: int     # Đ<PERSON> phì nhiêu tối ưu
    salinity_ops: int      # Độ mặn tối ưu

    # Các trường boolean (0/1) cho kết cấu đất tối ưu
    texture_ops_heavy: int     # Đất nặng
    texture_ops_light: int     # Đất nhẹ
    texture_ops_medium: int    # Đất trung bình
    texture_ops_organic: int   # Đất hữu cơ
    texture_ops_wide: int      # Đất có phạm vi rộng

    # Các trường boolean cho kết cấu đất tuyệt đối
    texture_abs_heavy: int     # Đất nặng
    texture_abs_light: int     # Đất nhẹ
    texture_abs_medium: int    # Đất trung bình
    texture_abs_organic: int   # Đất hữu cơ
    texture_abs_wide: int      # Đất có phạm vi rộng

    # Các trường cho khả năng thoát nước tối ưu
    drainage_opt_excessive: int = Field(       # Thoát nước quá mức
        alias='drainage_opt_excessive (dry/moderately dry)')
    drainage_opt_poorly: int = Field(          # Thoát nước kém
        alias='drainage_opt_poorly (saturated >50% of year)')
    drainage_opt_well: int = Field(            # Thoát nước tốt
        alias='drainage_opt_well (dry spells)')

    # Các trường cho khả năng thoát nước tuyệt đối
    drainage_abs_excessive: int = Field(       # Thoát nước quá mức
        alias='drainage_abs_excessive (dry/moderately dry)')
    drainage_abs_poorly: int = Field(          # Thoát nước kém
        alias='drainage_abs_poorly (saturated >50% of year)')
    drainage_abs_well: int = Field(            # Thoát nước tốt
        alias='drainage_abs_well (dry spells)')

    # Các trường cho vùng khí hậu
    climate_zone_boreal: int = Field(                      # Hàn đới
        alias='climate_zone_boreal (E)')
    climate_zone_desert: int = Field(                      # Sa mạc hoặc khô hạn
        alias='climate_zone_desert or arid (Bw)')
    climate_zone_steppe: int = Field(                      # Thảo nguyên hoặc bán khô hạn
        alias='climate_zone_steppe or semiarid (Bs)')
    climate_zone_subtropical_dry_summer: int = Field(      # Cận nhiệt đới khô hạn mùa hè
        alias='climate_zone_subtropical dry summer (Cs)')
    climate_zone_subtropical_dry_winter: int = Field(      # Cận nhiệt đới khô hạn mùa đông
        alias='climate_zone_subtropical dry winter (Cw)')
    climate_zone_subtropical_humid: int = Field(           # Cận nhiệt đới ẩm
        alias='climate_zone_subtropical humid (Cf)')
    climate_zone_temperate_continental: int = Field(       # Ôn đới lục địa
        alias='climate_zone_temperate continental (Dc)')
    climate_zone_temperate_oceanic: int = Field(          # Ôn đới hải dương
        alias='climate_zone_temperate oceanic (Do)')
    climate_zone_temperate_dry_winters: int = Field(      # Ôn đới khô hạn mùa đông
        alias='climate_zone_temperate with dry winters (Dw)')
    climate_zone_temperate_humid_winters: int = Field(    # Ôn đới ẩm ướt mùa đông
        alias='climate_zone_temperate with humid winters (Df)')
    climate_zone_tropical_wet_dry: int = Field(          # Nhiệt đới mưa và khô
        alias='climate_zone_tropical wet & dry (Aw)')
    climate_zone_tropical_wet: int = Field(              # Nhiệt đới ẩm
        alias='climate_zone_tropical wet (Ar)')

    # Các trường cho quang kỳ
    photoperiod_long_day: int = Field(                   # Ngày dài
        alias='photoperiod_long day (>14 hours)')
    photoperiod_neutral_day: int = Field(                # Ngày trung tính
        alias='photoperiod_neutral day (12-14 hours)')
    photoperiod_not_sensitive: int = Field(              # Không nhạy cảm
        alias='photoperiod_not sensitive')
    photoperiod_short_day: int = Field(                  # Ngày ngắn
        alias='photoperiod_short day (<12 hours)')

    def as_dict(self) -> Dict[str, Any]:
        # Chuyển đổi tất cả các giá trị số nguyên sang số thực cho quá trình scale
        return {k: float(v) if isinstance(v, int) else v
                for k, v in self.model_dump(by_alias=True).items()}


class RFCropRequest(BaseModel):
    """
    Model cho request gợi ý cây trồng sử dụng Random Forest
    """
    N: float  # Nitrogen content
    P: float  # Phosphorus content
    K: float  # Potassium content
    temperature: float  # Temperature in Celsius
    humidity: float  # Humidity percentage
    ph: float  # pH value
    rainfall: float  # Rainfall in mm
