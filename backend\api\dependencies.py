from fastapi import Depends
from ..ml.knn_model import KNNModel
from ..ml.rf_model import RandomForestModel
from ..services.data_service import DataService
from ..services.suggestion_service import SuggestionService
from ..core.config import settings

def get_data_service():
    """
    Dependency để lấy instance của DataService
    
    Returns:
        DataService: Instance của DataService
    """
    return DataService(
        species_data_path=str(settings.SPECIES_DATA_PATH)
    )

def get_knn_model():
    """
    Dependency để lấy instance của KNNModel
    
    Returns:
        KNNModel: Instance của KNNModel
    """
    return KNNModel(
        model_path=str(settings.KNN_MODEL_PATH),
        scaler_path=str(settings.SCALER_PATH),
        labels_path=str(settings.LABELS_PATH)
    )

def get_rf_model():
    """
    Dependency để lấy instance của RandomForestModel
    
    Returns:
        RandomForestModel: Instance của RandomForestModel
    """
    return RandomForestModel(
        model_path=str(settings.RF_MODEL_PATH)
    )

def get_knn_suggestion_service(
    model: KNNModel = Depends(get_knn_model),
    data_service: DataService = Depends(get_data_service)
):
    """
    Dependency để lấy instance của SuggestionService cho KNN
    
    Args:
        model: Instance của KNNModel
        data_service: Instance của DataService
    
    Returns:
        SuggestionService: Instance của SuggestionService
    """
    return SuggestionService(model, data_service)

def get_rf_suggestion_service(
    model: RandomForestModel = Depends(get_rf_model),
    data_service: DataService = Depends(get_data_service)
):
    """
    Dependency để lấy instance của SuggestionService cho Random Forest
    
    Args:
        model: Instance của RandomForestModel
        data_service: Instance của DataService
    
    Returns:
        SuggestionService: Instance của SuggestionService
    """
    return SuggestionService(model, data_service)
