# CHƯƠNG 1: TỔNG QUAN VỀ VẤN ĐỀ NGHIÊN CỨU

## 1.1. T<PERSON><PERSON> hình nghiên cứu trong và ngoài nước

### 1.1.1. T<PERSON><PERSON> hình nghiên cứu ngoài nước

Việc ứng dụng Machine Learning trong nông nghiệp đã được nghiên cứu và phát triển mạnh mẽ tại nhiều quốc gia phát triển trong thập kỷ qua.

**Tại Hoa Kỳ:**
Các nghiên cứu của <PERSON> và IBM đã phát triển hệ thống Watson Decision Platform for Agriculture, sử dụng AI để tối ưu hóa việc lựa chọn giống cây trồng dựa trên dữ liệu thời tiết, đất đai và lịch sử canh tác [1]. Hệ thống này đã được triển khai rộng rãi và cho thấy hiệu quả tăng năng suất 10-15%.

**Tại châu Âu:**
Dự án APOLLO (2019-2022) của Liên minh châu Âu đã phát triển nền tảng AI cho nông nghiệp thông minh, trong đó có module gợi ý cây trồng sử dụng thuật toán ensemble learning kết hợp Random Forest và Support Vector Machine [2]. Kết quả cho thấy độ chính xác đạt 87% trong việc dự đoán cây trồng phù hợp.

**Tại Ấn Độ:**
Nghiên cứu của Rakesh Kumar và cộng sự (2021) đã phát triển hệ thống CropAdvisor sử dụng thuật toán KNN và Naive Bayes để gợi ý cây trồng cho nông dân Ấn Độ [3]. Hệ thống đã được thử nghiệm tại 5 bang và cho thấy độ chính xác 82%.

### 1.1.2. Tình hình nghiên cứu trong nước

Tại Việt Nam, việc ứng dụng AI trong nông nghiệp còn đang trong giai đoạn khởi đầu nhưng đã có những bước tiến đáng kể.

**Các nghiên cứu học thuật:**
- Nghiên cứu của TS. Nguyễn Văn Hiếu (Đại học Nông nghiệp Hà Nội, 2020) về ứng dụng machine learning trong dự báo năng suất lúa [4]
- Luận án tiến sĩ của Trần Thị Mai (Đại học Cần Thơ, 2021) về hệ thống hỗ trợ quyết định trong canh tác lúa sử dụng fuzzy logic [5]

**Các ứng dụng thực tiễn:**
- Ứng dụng "Nông nghiệp thông minh" của FPT đã tích hợp một số tính năng AI cơ bản
- Hệ thống của Viettel đã triển khai IoT và AI trong một số trang trại thí điểm

### 1.1.3. Đánh giá và nhận xét

**Ưu điểm của các nghiên cứu đã có:**
- Đã chứng minh tính khả thi và hiệu quả của việc ứng dụng ML trong nông nghiệp
- Có nhiều thuật toán đã được thử nghiệm và đánh giá
- Một số hệ thống đã được thương mại hóa thành công

**Hạn chế và khoảng trống:**
- Hầu hết các nghiên cứu tập trung vào một thuật toán cụ thể, ít có so sánh đa thuật toán
- Thiếu nghiên cứu chuyên sâu về điều kiện nông nghiệp Việt Nam
- Giao diện người dùng thường phức tạp, khó tiếp cận với nông dân
- Ít có hệ thống tích hợp đầy đủ từ backend đến frontend

## 1.2. Phương pháp nghiên cứu

### 1.2.1. Tổng quan các phương pháp có thể sử dụng

**Phương pháp Machine Learning:**
1. **Supervised Learning:**
   - K-Nearest Neighbors (KNN)
   - Random Forest
   - Support Vector Machine (SVM)
   - Neural Networks

2. **Unsupervised Learning:**
   - K-Means Clustering
   - Hierarchical Clustering

3. **Ensemble Methods:**
   - Bagging
   - Boosting
   - Voting Classifier

### 1.2.2. Lý do lựa chọn phương pháp

**Lựa chọn K-Nearest Neighbors (KNN):**
- **Ưu điểm:** Đơn giản, dễ hiểu và triển khai; không cần giả định về phân phối dữ liệu; hiệu quả với dữ liệu có nhiều chiều
- **Phù hợp:** Bài toán gợi ý cây trồng có tính chất "tương tự" - cây trồng phù hợp với điều kiện tương tự nhau
- **Lý do chọn:** Cho phép gợi ý nhiều lựa chọn (top-k), phù hợp với nhu cầu đa dạng hóa cây trồng

**Lựa chọn Random Forest:**
- **Ưu điểm:** Độ chính xác cao; xử lý tốt overfitting; có thể xử lý dữ liệu thiếu; cung cấp feature importance
- **Phù hợp:** Dữ liệu nông nghiệp thường có nhiều noise và missing values
- **Lý do chọn:** Cung cấp một lựa chọn tối ưu nhất với độ tin cậy cao

### 1.2.3. Phương pháp thu thập và xử lý dữ liệu

**Nguồn dữ liệu:**
1. **Crop Recommendation Dataset:** Bộ dữ liệu chuẩn từ Kaggle với 2200 mẫu, 22 loại cây trồng
2. **Merged Species Dataset:** Dữ liệu chi tiết về 2000+ loài cây từ các nguồn nông nghiệp
3. **Environmental Features Dataset:** Dữ liệu đặc trưng môi trường đã được xử lý

**Phương pháp xử lý dữ liệu:**
1. **Data Cleaning:** Loại bỏ outliers, xử lý missing values
2. **Feature Engineering:** Chuẩn hóa, scaling, encoding categorical variables
3. **Data Validation:** Kiểm tra tính nhất quán và logic của dữ liệu

### 1.2.4. Phương pháp đánh giá mô hình

**Metrics đánh giá:**
- Accuracy: Độ chính xác tổng thể
- Precision: Độ chính xác cho từng class
- Recall: Khả năng phát hiện đúng
- F1-Score: Cân bằng giữa Precision và Recall

**Phương pháp validation:**
- Train-Test Split (80-20)
- Cross-Validation (5-fold)
- Stratified Sampling để đảm bảo cân bằng dữ liệu

## 1.3. Kiến trúc hệ thống và công nghệ sử dụng

### 1.3.1. Kiến trúc tổng thể

Hệ thống được thiết kế theo mô hình microservices với các thành phần chính:

```
Frontend (React) ←→ API Gateway ←→ Backend Services
                                    ├── KNN Service (Port 8000)
                                    └── RF Service (Port 8001)
                                           ↓
                                    Data Layer (CSV/Future DB)
```

### 1.3.2. Công nghệ Backend

**Framework chính:** FastAPI
- **Lý do chọn:** Hiệu suất cao, hỗ trợ async/await, tự động tạo documentation
- **Ưu điểm:** Type hints, validation tự động, dễ testing

**Machine Learning:** scikit-learn
- **Lý do chọn:** Thư viện ML phổ biến nhất Python, ổn định, đầy đủ tính năng
- **Hỗ trợ:** Cả KNN và Random Forest, preprocessing tools

**Dependency Injection:** FastAPI Depends
- **Mục đích:** Tách biệt logic, dễ testing và maintenance
- **Cấu trúc:** Service layer, Repository pattern

### 1.3.3. Công nghệ Frontend

**Framework:** React 18
- **Lý do chọn:** Component-based, virtual DOM, ecosystem phong phú
- **State Management:** useState hooks cho local state

**Styling:** TailwindCSS + Radix UI
- **Lý do chọn:** Utility-first, responsive design, accessibility
- **Components:** Radix UI cho các component phức tạp

**HTTP Client:** Axios
- **Tính năng:** Request/response interceptors, error handling

## 1.4. Mô tả cấu trúc báo cáo

### 1.4.1. Vai trò và nội dung các chương

**Chương 1 - Tổng quan về vấn đề nghiên cứu:**
- Vai trò: Cung cấp bối cảnh và cơ sở lý luận cho nghiên cứu
- Nội dung: Tình hình nghiên cứu, phương pháp luận, kiến trúc hệ thống

**Chương 2 - Cơ sở lý thuyết:**
- Vai trò: Xây dựng nền tảng lý thuyết cho việc triển khai
- Nội dung: Thuật toán ML, kiến trúc phần mềm, công nghệ web

**Chương 3 - Kết quả nghiên cứu và thảo luận:**
- Vai trò: Trình bày quá trình thực hiện và kết quả đạt được
- Nội dung: Triển khai hệ thống, training models, đánh giá hiệu quả

**Chương 4 - Kết luận và kiến nghị:**
- Vai trò: Tổng kết và định hướng phát triển
- Nội dung: Đánh giá tổng thể, hạn chế, hướng phát triển

### 1.4.2. Mối liên hệ giữa các chương

```
Chương 1 (Tổng quan) → Chương 2 (Lý thuyết) → Chương 3 (Thực hiện) → Chương 4 (Kết luận)
       ↓                      ↓                      ↓                      ↓
   Xác định vấn đề      Cơ sở khoa học        Giải pháp cụ thể        Đánh giá tổng thể
```

Mỗi chương đều có mối liên hệ chặt chẽ:
- Chương 1 đặt nền móng cho toàn bộ nghiên cứu
- Chương 2 cung cấp công cụ lý thuyết để thực hiện
- Chương 3 áp dụng lý thuyết vào thực tiễn
- Chương 4 đánh giá và định hướng tương lai

Cấu trúc này đảm bảo tính logic, khoa học và dễ theo dõi của toàn bộ báo cáo.
