import joblib
import pandas as pd
import os
import logging
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
from .utils import CropInfo

# Thiết lập logging với định dạng chi tiết hơn
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Lấy đường dẫn tuyệt đối đến thư mục chứa model và data
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
MODEL_DIR = os.path.join(BASE_DIR, 'model')
DATA_DIR = os.path.join(BASE_DIR, 'data')

logger.info(f"Đang tải models từ {MODEL_DIR} và data từ {DATA_DIR}")

class CropSpeciesInfo(BaseModel):
    species_code: Any  # Cho phép cả int và str
    species_name: Optional[str] = None
    life_form: Optional[str] = None
    habit: Optional[str] = None
    life_span: Optional[str] = None
    physiology: Optional[str] = None
    category: Optional[str] = None
    growing_cycle: Optional[str] = None
    main_use: Optional[str] = None
    detailed_use: Optional[str] = None
    used_part: Optional[str] = None

try:
    # Tải các artifacts một lần khi khởi động server
    scaler = joblib.load(os.path.join(MODEL_DIR, 'scaler.joblib'))      # Bộ chuẩn hóa dữ liệu
    logger.info("Đã tải scaler thành công")

    nn_model = joblib.load(os.path.join(MODEL_DIR, 'nn_model.joblib'))  # Model K-Nearest Neighbors
    logger.info("Đã tải model KNN thành công")

    labels = joblib.load(os.path.join(MODEL_DIR, 'labels.joblib'))      # Nhãn của các cây trồng
    logger.info("Đã tải labels thành công")

    # Tải thông tin species
    species_data = pd.read_csv(os.path.join(DATA_DIR, 'merged_species.csv'))
    logger.info("Đã tải thông tin species thành công")

    # Tên các đặc trưng (để đảm bảo thứ tự chính xác)
    feature_names = scaler.feature_names_in_
    logger.info("Tên các đặc trưng cần thiết:")
    for name in feature_names:
        logger.info(f"  - {name}")

    # Hàm helper để xử lý giá trị NaN
    def safe_get(row, column, default=None):
        value = row.get(column, default)
        # Kiểm tra nếu giá trị là NaN (pd.isna) và trả về None
        if pd.isna(value):
            return None
        return value



    # Tải thông tin chi tiết về species
    SPECIES_INFO = {}
    for _, row in species_data.iterrows():
        try:
            # Chuyển đổi species_code thành chuỗi để tránh lỗi validation
            species_code = row['species_code']
            species_code_str = str(species_code) if not pd.isna(species_code) else ""

            SPECIES_INFO[species_code] = CropSpeciesInfo(
                species_code=species_code_str,
                species_name=safe_get(row, 'species'),  # Tên cột trong CSV là 'species'
                life_form=safe_get(row, 'Life.form'),  # Tên cột có dấu chấm thay vì dấu cách
                habit=safe_get(row, 'Habit'),
                life_span=safe_get(row, 'Life.span'),
                physiology=safe_get(row, 'Physiology'),
                category=safe_get(row, 'Category'),
                growing_cycle=None,  # Sẽ được tính toán sau khi lấy thông tin chi tiết
                main_use=safe_get(row, 'use.main'),
                detailed_use=safe_get(row, 'use.detailed'),
                used_part=safe_get(row, 'use.part')
            )
        except Exception as e:
            logger.warning(f"Không thể tải thông tin cho species {row.get('species_code', 'unknown')}: {str(e)}")
            continue

    logger.info(f"Đã tải thông tin chi tiết cho {len(SPECIES_INFO)} species thành công")

except Exception as e:
    logger.error(f"Lỗi khi tải models và data: {str(e)}")
    raise

# Định nghĩa CropResponse đã được chuyển sang utils.py

# Hàm định dạng chu kỳ phát triển
def _format_growing_cycle(min_cycle, max_cycle):
    if pd.isna(min_cycle) and pd.isna(max_cycle):
        return None
    if pd.isna(min_cycle):
        return f"Up to {max_cycle} days"
    if pd.isna(max_cycle):
        return f"At least {min_cycle} days"
    if min_cycle == max_cycle:
        return f"{min_cycle} days"
    return f"{min_cycle}-{max_cycle} days"

def get_crop_info(crop_id: int) -> CropInfo:
    """Get detailed information about a crop from the species data."""
    crop_data = species_data[species_data['species_code'] == crop_id].iloc[0]

    # Xử lý các giá trị NaN
    def safe_value(value):
        return None if pd.isna(value) else value

    # Đối với các trường số nguyên, cần chuyển đổi thành int nếu không phải NaN
    def safe_int(value, default=0):
        return default if pd.isna(value) else int(value)

    # Định dạng chu kỳ phát triển
    growing_cycle = _format_growing_cycle(safe_value(crop_data['cycle_min']), safe_value(crop_data['cycle_max']))

    return CropInfo(
        id=crop_id,
        species_name=safe_value(crop_data['species']) or "Unknown",
        life_form=safe_value(crop_data['Life.form']) or "Unknown",
        habit=safe_value(crop_data['Habit']) or "Unknown",
        life_span=safe_value(crop_data['Life.span']) or "Unknown",
        physiology=safe_value(crop_data['Physiology']),
        category=safe_value(crop_data['Category']) or "Unknown",
        plant_attributes=safe_value(crop_data['Plant.attributes']),
        growing_cycle=growing_cycle,
        main_use=safe_value(crop_data['use.main']) or "Unknown",
        detailed_use=safe_value(crop_data['use.detailed']),
        used_part=safe_value(crop_data['use.part']) or "Unknown",
        min_growing_cycle=safe_int(crop_data['cycle_min']),
        max_growing_cycle=safe_int(crop_data['cycle_max'])
    )

def suggest_crops(input_dict: Dict[str, Any]) -> List[CropInfo]:
    """
    Hàm gợi ý cây trồng dựa trên các đặc trưng đầu vào.

    Args:
        input_dict (Dict[str, Any]): Dictionary chứa các đặc trưng của điều kiện trồng trọt

    Returns:
        List[CropInfo]: Danh sách thông tin chi tiết của 5 cây trồng được gợi ý

    Raises:
        ValueError: Nếu dữ liệu đầu vào không hợp lệ hoặc thiếu
    """
    try:
        logger.info("Đã nhận được dữ liệu đầu vào với các trường:")
        for key in input_dict.keys():
            logger.info(f"  - {key}")

        # Kiểm tra dữ liệu đầu vào
        required_columns = set(feature_names)
        input_columns = set(input_dict.keys())

        missing_columns = required_columns - input_columns
        if missing_columns:
            raise ValueError(f"Thiếu các trường bắt buộc: {missing_columns}")

        # Tạo DataFrame với thứ tự cột chính xác
        try:
            df_new = pd.DataFrame([input_dict], columns=feature_names)
            logger.info(f"Đã tạo DataFrame với kích thước: {df_new.shape}")
        except Exception as e:
            raise ValueError(f"Lỗi khi tạo DataFrame: {str(e)}. Cần các cột: {feature_names.tolist()}")

        # Kiểm tra giá trị số
        numeric_columns = df_new.select_dtypes(include=['float64', 'int64']).columns
        for col in numeric_columns:
            if pd.isna(df_new[col]).any():
                raise ValueError(f"Giá trị không hợp lệ trong cột {col}")

        try:
            # Chuẩn hóa dữ liệu đầu vào
            X_new_scaled = scaler.transform(df_new)
            logger.info(f"Đã chuẩn hóa dữ liệu đầu vào")
        except Exception as e:
            raise ValueError(f"Lỗi khi chuẩn hóa dữ liệu: {str(e)}")

        try:
            # Tìm k láng giềng gần nhất
            dists, idxs = nn_model.kneighbors(X_new_scaled)
            logger.info(f"Đã tìm thấy các láng giềng gần nhất. Khoảng cách: {dists[0]}")
        except Exception as e:
            raise ValueError(f"Lỗi khi tìm láng giềng gần nhất: {str(e)}")

        # Lấy mã cây trồng từ các láng giềng gần nhất và thông tin chi tiết
        crop_ids = [int(labels[i]) for i in idxs[0]]
        results = [get_crop_info(crop_id) for crop_id in crop_ids]

        logger.info(f"Đã lấy thông tin chi tiết cho {len(results)} cây trồng được gợi ý")

        return results
    except Exception as e:
        logger.error(f"Lỗi trong hàm suggest_crops: {str(e)}")
        raise
