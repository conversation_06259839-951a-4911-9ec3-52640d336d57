from fastapi import FastAP<PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import logging
from typing import List, Optional, Dict, Any

from .core.config import settings
from .core.logging import logger
from .api.routes.knn import router as knn_router
from .api.models.requests import CropRequest
from .api.models.responses import CropResponse
from .api.dependencies import get_knn_suggestion_service
from .services.suggestion_service import SuggestionService

class EnvironmentFeatures(BaseModel):
    temperature: float
    humidity: float
    rainfall: float
    pH: float
    N: float
    P: float
    K: float

app = FastAPI(
    title="API Gợi ý cây trồng",
    description="API cung cấp chức năng gợi ý cây trồng dựa trên điều kiện môi trường",
    version="1.0.0"
)

# Cấu hình CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Thêm router
app.include_router(knn_router)

@app.get("/")
async def root():
    """
    Root endpoint
    """
    return {
        "message": "Chào mừng đến với API Gợi ý cây trồng",
        "docs": "/docs",
        "version": "1.0.0"
    }

@app.post("/suggest_crops/")
async def suggest_crops_endpoint(
    features: EnvironmentFeatures,
    suggestion_service: SuggestionService = Depends(get_knn_suggestion_service)
):
    try:
        # Convert model to dictionary
        input_dict = features.model_dump()

        # Get crop suggestions with detailed information
        suggested_crops = suggestion_service.get_suggestions(input_dict)

        # Trả về kết quả dưới dạng CropResponse
        return CropResponse(suggestions=suggested_crops)
    except Exception as e:
        logger.error(f"Error in /suggest_crops endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
