import logging
from .config import settings

def setup_logging():
    """
    <PERSON><PERSON><PERSON><PERSON> lập c<PERSON>u hình logging cho ứng dụng
    """
    logging.basicConfig(
        level=getattr(logging, settings.LOG_LEVEL),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Tạo logger cho ứng dụng
    logger = logging.getLogger("crop_suggestion")
    
    return logger

# Tạo logger để sử dụng trong ứng dụng
logger = setup_logging()
