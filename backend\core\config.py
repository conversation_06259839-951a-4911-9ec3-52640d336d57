import os
from pathlib import Path
from typing import List, Union
from dotenv import load_dotenv

# Tải biến môi trường từ file .env
load_dotenv()

class Settings:
    """
    Cấu hình ứng dụng
    """
    # Đường dẫn cơ sở
    BASE_DIR = Path(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

    # Đường dẫn đến thư mục model
    MODEL_DIR = Path(os.getenv("MODEL_DIR", BASE_DIR / "model"))

    # Đường dẫn đến thư mục data
    DATA_DIR = Path(os.getenv("DATA_DIR", BASE_DIR / "data"))

    # Đường dẫn đến các file model
    KNN_MODEL_PATH = Path(os.getenv("KNN_MODEL_PATH", MODEL_DIR / "nn_model.joblib"))
    RF_MODEL_PATH = Path(os.getenv("RF_MODEL_PATH", MODEL_DIR / "random_forest_default_model.joblib"))
    SCALER_PATH = Path(os.getenv("SCALER_PATH", MODEL_DIR / "scaler.joblib"))
    LABELS_PATH = Path(os.getenv("LABELS_PATH", MODEL_DIR / "labels.joblib"))

    # Đường dẫn đến các file dữ liệu
    SPECIES_DATA_PATH = Path(os.getenv("SPECIES_DATA_PATH", DATA_DIR / "merged_species.csv"))
    CROP_RECOMMENDATION_PATH = Path(os.getenv("CROP_RECOMMENDATION_PATH", DATA_DIR / "Crop_recommendation.csv"))

    # Cấu hình API
    API_PREFIX = os.getenv("API_PREFIX", "/api")
    KNN_API_PORT = int(os.getenv("KNN_API_PORT", 8000))
    RF_API_PORT = int(os.getenv("RF_API_PORT", 8001))
    HOST = os.getenv("HOST", "127.0.0.1")
    DEBUG = os.getenv("DEBUG", "True").lower() in ("true", "1", "t")

    # Cấu hình CORS
    CORS_ORIGINS: List[str] = os.getenv("CORS_ORIGINS", '["*"]')
    if isinstance(CORS_ORIGINS, str):
        import json
        try:
            CORS_ORIGINS = json.loads(CORS_ORIGINS)
        except json.JSONDecodeError:
            CORS_ORIGINS = CORS_ORIGINS.split(",")

    # Cấu hình logging
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

# Tạo instance settings để sử dụng trong ứng dụng
settings = Settings()
