import joblib
import pandas as pd
from typing import Dict, Any, List
from .base_model import BaseModel
from ..core.logging import logger
from ..core.exceptions import ModelNotFoundException, PredictionException, InvalidInputException

class KNNModel(BaseModel):
    """
    <PERSON>ô hình K-Nearest Neighbors
    """
    def __init__(self, model_path: str, scaler_path: str, labels_path: str):
        """
        Khởi tạo mô hình KNN
        
        Args:
            model_path: Đường dẫn đến file model KNN
            scaler_path: Đường dẫn đến file scaler
            labels_path: Đường dẫn đến file labels
        """
        super().__init__(model_path)
        self.scaler_path = scaler_path
        self.labels_path = labels_path
        self.scaler = self._load_scaler()
        self.labels = self._load_labels()
        self.feature_names = self.scaler.feature_names_in_
    
    def _load_scaler(self):
        """
        Tải scaler từ file
        
        Returns:
            Scaler đã tải
        
        Raises:
            ModelNotFoundException: <PERSON>ếu không tìm thấy scaler
        """
        try:
            logger.info(f"Đang tải scaler từ {self.scaler_path}")
            scaler = joblib.load(self.scaler_path)
            logger.info(f"Đã tải scaler thành công")
            return scaler
        except Exception as e:
            logger.error(f"Lỗi khi tải scaler: {str(e)}")
            raise ModelNotFoundException(f"Không thể tải scaler từ {self.scaler_path}: {str(e)}")
    
    def _load_labels(self):
        """
        Tải labels từ file
        
        Returns:
            Labels đã tải
        
        Raises:
            ModelNotFoundException: Nếu không tìm thấy labels
        """
        try:
            logger.info(f"Đang tải labels từ {self.labels_path}")
            labels = joblib.load(self.labels_path)
            logger.info(f"Đã tải labels thành công")
            return labels
        except Exception as e:
            logger.error(f"Lỗi khi tải labels: {str(e)}")
            raise ModelNotFoundException(f"Không thể tải labels từ {self.labels_path}: {str(e)}")
    
    def predict(self, features: Dict[str, Any]) -> List[int]:
        """
        Dự đoán dựa trên đặc trưng đầu vào
        
        Args:
            features: Đặc trưng đầu vào
        
        Returns:
            Danh sách các ID cây trồng được gợi ý
        
        Raises:
            InvalidInputException: Nếu dữ liệu đầu vào không hợp lệ
            PredictionException: Nếu có lỗi trong quá trình dự đoán
        """
        try:
            logger.info("Đang xử lý dữ liệu đầu vào")
            
            # Kiểm tra dữ liệu đầu vào
            required_columns = set(self.feature_names)
            input_columns = set(features.keys())
            
            missing_columns = required_columns - input_columns
            if missing_columns:
                raise InvalidInputException(f"Thiếu các trường bắt buộc: {missing_columns}")
            
            # Tạo DataFrame với thứ tự cột chính xác
            df_new = pd.DataFrame([features], columns=self.feature_names)
            logger.info(f"Đã tạo DataFrame với kích thước: {df_new.shape}")
            
            # Kiểm tra giá trị số
            numeric_columns = df_new.select_dtypes(include=['float64', 'int64']).columns
            for col in numeric_columns:
                if pd.isna(df_new[col]).any():
                    raise InvalidInputException(f"Giá trị không hợp lệ trong cột {col}")
            
            # Chuẩn hóa dữ liệu đầu vào
            X_new_scaled = self.scaler.transform(df_new)
            logger.info(f"Đã chuẩn hóa dữ liệu đầu vào")
            
            # Tìm k láng giềng gần nhất
            dists, idxs = self.model.kneighbors(X_new_scaled)
            logger.info(f"Đã tìm thấy các láng giềng gần nhất. Khoảng cách: {dists[0]}")
            
            # Lấy mã cây trồng từ các láng giềng gần nhất
            crop_ids = [int(self.labels[i]) for i in idxs[0]]
            logger.info(f"Đã tìm thấy các cây trồng: {crop_ids}")
            
            return crop_ids
            
        except InvalidInputException as e:
            logger.error(f"Lỗi dữ liệu đầu vào: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Lỗi khi dự đoán: {str(e)}")
            raise PredictionException(f"Lỗi khi dự đoán: {str(e)}")
