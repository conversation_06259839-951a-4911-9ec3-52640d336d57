import { useState } from 'react';
import KNNPage from './components/KNNPage';
import RFPage from './components/RFPage';

export default function App() {
  const [activeModel, setActiveModel] = useState('knn'); // 'knn' hoặc 'rf'

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-6">
        {/* Thanh điều hướng */}
        <div className="flex justify-center mb-8">
          <div className="bg-white rounded-lg shadow-md p-2 inline-flex">
            <button
              onClick={() => setActiveModel('knn')}
              className={`px-4 py-2 rounded-md transition-all ${
                activeModel === 'knn'
                  ? 'bg-green-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <PERSON><PERSON> hình KNN
            </button>
            <button
              onClick={() => setActiveModel('rf')}
              className={`px-4 py-2 rounded-md ml-2 transition-all ${
                activeModel === 'rf'
                  ? 'bg-green-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Mô hình Random Forest
            </button>
          </div>
        </div>

        {/* Hiển thị component tương ứng */}
        <div className="flex items-center justify-center">
          {activeModel === 'knn' ? <KNNPage /> : <RFPage />}
        </div>
      </div>
    </div>
  );
}
