#!/bin/bash

# Kiểm tra môi trường ảo Python
if [ ! -d ".venv" ]; then
    echo "Tạo môi trường ảo Python..."
    python -m venv .venv
fi

# Kích hoạt môi trường ảo
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    # Windows
    source .venv/Scripts/activate
else
    # Linux/Mac
    source .venv/bin/activate
fi

# Cài đặt các thư viện Python
echo "Cài đặt các thư viện Python..."
pip install -r requirements.txt

# Khởi động backend trong nền
echo "Khởi động backend..."
uvicorn backend.main:app --reload &
BACKEND_PID=$!

# Di chuyển vào thư mục frontend
cd frontend

# Cài đặt các thư viện Node.js
echo "Cài đặt các thư viện Node.js..."
npm install

# Khởi động frontend
echo "Khởi động frontend..."
npm start &
FRONTEND_PID=$!

# Mở trình duyệt
sleep 5
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    # Windows
    start http://localhost:3000
else
    # Linux/Mac
    if command -v xdg-open &> /dev/null; then
        xdg-open http://localhost:3000
    elif command -v open &> /dev/null; then
        open http://localhost:3000
    fi
fi

# Xử lý khi nhấn Ctrl+C
trap "kill $BACKEND_PID $FRONTEND_PID; exit" INT

# Giữ script chạy
echo "Hệ thống đang chạy. Nhấn Ctrl+C để dừng."
wait
