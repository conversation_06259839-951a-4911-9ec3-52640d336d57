from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as f:
    requirements = f.read().splitlines()

setup(
    name="crop-suggestion-system",
    version="1.0.0",
    author="<PERSON><PERSON><PERSON><PERSON>",
    author_email="<EMAIL>",
    description="Hệ thống gợi ý cây trồng thông minh",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/crop-suggestion-system",
    packages=find_packages(),
    classifiers=[
        "Programming Language :: Python :: 3",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
)
