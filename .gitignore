# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
.venv/
venv/
ENV/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
.pytest_cache/
.coverage
htmlcov/

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production

# Model files
# *.joblib - Commented out to include model files in the repository for convenience

# Jupyter Notebook
.ipynb_checkpoints
*.ipynb

# Node.js
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
.pnp/
.pnp.js

# Frontend build
/frontend/build
/frontend/coverage
/frontend/.env.local
/frontend/.env.development.local
/frontend/.env.test.local
/frontend/.env.production.local

# IDE
.idea/
.vscode/
*.swp
*.swo
*.sublime-workspace
*.sublime-project

# OS
.DS_Store
Thumbs.db
.directory
desktop.ini

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Temporary files
*.tmp
*.bak
*.swp
*~

# Documentation
/docs/_build/
