from fastapi import APIRouter, Depends, HTTPException
from typing import Dict, Any
from ...core.logging import logger
from ...core.exceptions import convert_exception_to_http_exception
from ...services.suggestion_service import SuggestionService
from ..dependencies import get_knn_suggestion_service
from ..models.requests import CropRequest
from ..models.responses import CropResponse

router = APIRouter(tags=["KNN"])

@router.post('/suggest', response_model=CropResponse)
async def suggest(
    req: CropRequest, 
    suggestion_service: SuggestionService = Depends(get_knn_suggestion_service)
):
    """
    API endpoint để gợi ý cây trồng phù hợp với điều kiện môi trường sử dụng mô hình KNN.

    Args:
        req (CropRequest): Object chứa thông tin về điều kiện môi trường
        suggestion_service (SuggestionService): Dịch vụ gợi ý cây trồng

    Returns:
        CropResponse: Object chứa danh sách thông tin chi tiết của các cây trồng được gợi ý

    Raises:
        HTTPException: Nếu có lỗi xảy ra trong quá trình xử lý
    """
    try:
        # Chuyển đổi request thành dictionary và gọi dịch vụ gợi ý
        input_data = req.as_dict()
        suggested_crops = suggestion_service.get_suggestions(input_data)
        return CropResponse(suggestions=suggested_crops)
    except Exception as e:
        # Log lỗi và trả về response lỗi
        logger.error(f"Error in /suggest endpoint: {str(e)}")
        raise convert_exception_to_http_exception(e)
