import pandas as pd
from typing import Dict, Any, List, Set
from ..core.logging import logger
from ..core.exceptions import InvalidInputException

def validate_numeric_values(data: Dict[str, Any], required_fields: List[str] = None) -> None:
    """
    Kiểm tra tính hợp lệ của các giá trị số trong dữ liệu đầu vào
    
    Args:
        data: Dữ liệu đầu vào
        required_fields: <PERSON>h sách các trường bắt buộc
    
    Raises:
        InvalidInputException: Nếu dữ liệu đầu vào không hợp lệ
    """
    if required_fields:
        # Kiểm tra các trường bắt buộc
        missing_fields = [field for field in required_fields if field not in data]
        if missing_fields:
            raise InvalidInputException(f"Thiếu các trường bắt buộc: {', '.join(missing_fields)}")
    
    # Kiểm tra giá trị số
    for key, value in data.items():
        if isinstance(value, (int, float)):
            if pd.isna(value):
                raise InvalidInputException(f"Gi<PERSON> trị không hợp lệ trong trường {key}")

def validate_input_features(features: Dict[str, Any], expected_features: Set[str]) -> None:
    """
    Kiểm tra tính hợp lệ của các đặc trưng đầu vào
    
    Args:
        features: Đặc trưng đầu vào
        expected_features: Tập hợp các đặc trưng mong đợi
    
    Raises:
        InvalidInputException: Nếu đặc trưng đầu vào không hợp lệ
    """
    # Kiểm tra các đặc trưng bắt buộc
    input_features = set(features.keys())
    missing_features = expected_features - input_features
    if missing_features:
        raise InvalidInputException(f"Thiếu các đặc trưng bắt buộc: {', '.join(missing_features)}")
    
    # Kiểm tra giá trị số
    validate_numeric_values(features)
