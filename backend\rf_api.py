from fastapi import FastAP<PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from typing import List, Dict, Any

from .core.config import settings
from .core.logging import logger
from .api.routes.rf import router as rf_router
from .api.models.requests import RFCropRequest
from .api.models.responses import RFCropResponse
from .api.dependencies import get_rf_suggestion_service
from .services.suggestion_service import SuggestionService

# Tạo ứng dụng FastAPI
app = FastAPI(
    title="API Gợi ý cây trồng - Random Forest",
    description="API cung cấp chức năng gợi ý cây trồng dựa trên mô hình Random Forest",
    version="1.0.0"
)

# Cấu hình CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Thêm router với prefix
app.include_router(rf_router, prefix="/rf")

@app.get("/")
async def root():
    """
    Root endpoint
    """
    return {
        "message": "Chào mừng đến với API Gợi ý cây trồng - Random Forest",
        "docs": "/docs",
        "version": "1.0.0"
    }
