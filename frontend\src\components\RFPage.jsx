import React from 'react';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import RFCropSuggestionUI from './ui/RFCropSuggestionUI';

const RFPage = () => {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold text-center mb-8">
        <PERSON>ệ thống gợi ý cây trồng sử dụng Random Forest
      </h1>
      <p className="text-center text-gray-600 mb-8">
        <PERSON><PERSON> thống này sử dụng mô hình Random Forest để gợi ý cây trồng phù hợp nhất dựa trên các điều kiện môi trường.
      </p>
      
      <RFCropSuggestionUI />
      
      <ToastContainer position="bottom-right" />
    </div>
  );
};

export default RFPage;
