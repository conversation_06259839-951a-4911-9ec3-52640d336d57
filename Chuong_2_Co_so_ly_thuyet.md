# CHƯƠNG 2: CƠ SỞ LÝ THUYẾT

## 2.1. <PERSON><PERSON> sở lý thuyết về Machine Learning

### 2.1.1. <PERSON><PERSON><PERSON><PERSON>m Machine Learning

Machine Learning (Học máy) là một nhánh của trí tuệ nhân tạo (AI) cho phép máy tính học hỏi và đưa ra quyết định từ dữ liệu mà không cần được lập trình cụ thể cho từng tác vụ [6]. Trong bối cảnh nông nghiệp, ML giúp phân tích các mối quan hệ phức tạp giữa điều kiện môi trường và sự phát triển của cây trồng.

**Phân loại Machine Learning:**
1. **Supervised Learning (Học có giám sát):** Học từ dữ liệu đã đượ<PERSON> gán nh<PERSON>
2. **Unsupervised Learning (<PERSON><PERSON><PERSON> không giám sát):** T<PERSON><PERSON> ra patterns từ dữ liệu chưa gán nhãn
3. **Reinforcement Learning (Học tăng cường):** Học thông qua tương tác với môi trường

### 2.1.2. Supervised Learning trong bài toán phân loại

Bài toán gợi ý cây trồng thuộc dạng **classification problem** trong supervised learning, nơi:
- **Input (X):** Vector đặc trưng môi trường (nhiệt độ, độ ẩm, pH, NPK, v.v.)
- **Output (y):** Nhãn loại cây trồng phù hợp
- **Mục tiêu:** Học hàm f: X → y để dự đoán cây trồng cho điều kiện mới

## 2.2. Thuật toán K-Nearest Neighbors (KNN)

### 2.2.1. Nguyên lý hoạt động

KNN là thuật toán lazy learning dựa trên giả định rằng các điểm dữ liệu tương tự nhau sẽ có cùng nhãn [7]. Thuật toán hoạt động theo các bước:

1. **Tính khoảng cách:** Với điểm dữ liệu mới x, tính khoảng cách đến tất cả điểm trong tập training
2. **Tìm k láng giềng gần nhất:** Chọn k điểm có khoảng cách nhỏ nhất
3. **Voting:** Dự đoán nhãn dựa trên majority vote của k láng giềng

### 2.2.2. Công thức toán học

**Khoảng cách Euclidean:**
```
d(x, xi) = √(Σ(xj - xij)²)
```

**Dự đoán cho classification:**
```
ŷ = argmax Σ I(yi = c)
    c    i∈Nk(x)
```
Trong đó:
- Nk(x): Tập k láng giềng gần nhất của x
- I(): Hàm indicator
- c: Các class có thể

### 2.2.3. Ưu điểm và nhược điểm

**Ưu điểm:**
- Đơn giản, dễ hiểu và triển khai
- Không cần giả định về phân phối dữ liệu
- Hiệu quả với dữ liệu có ranh giới phức tạp
- Có thể trả về top-k kết quả

**Nhược điểm:**
- Tốn thời gian tính toán khi dự đoán
- Nhạy cảm với outliers và noise
- Hiệu quả giảm với dữ liệu high-dimensional

### 2.2.4. Ứng dụng trong gợi ý cây trồng

Trong hệ thống, KNN được sử dụng để:
- Tìm 5 cây trồng có điều kiện môi trường tương tự nhất
- Cung cấp đa dạng lựa chọn cho nông dân
- Xử lý dữ liệu đầu vào phức tạp với nhiều features

## 2.3. Thuật toán Random Forest

### 2.3.1. Nguyên lý hoạt động

Random Forest là ensemble method kết hợp nhiều Decision Trees [8]. Mỗi tree được training trên:
- **Bootstrap sample:** Mẫu con ngẫu nhiên từ tập training (bagging)
- **Random feature subset:** Tập con ngẫu nhiên các features tại mỗi node

### 2.3.2. Quy trình training

1. **Tạo bootstrap samples:** Từ tập training D có n mẫu, tạo B bootstrap samples
2. **Training trees:** Với mỗi sample, train một decision tree với random feature selection
3. **Ensemble:** Kết hợp B trees thành forest

**Công thức dự đoán:**
```
ŷ = majority_vote{T₁(x), T₂(x), ..., Tᵦ(x)}
```

### 2.3.3. Ưu điểm và nhược điểm

**Ưu điểm:**
- Độ chính xác cao, ít overfitting
- Xử lý tốt missing values và outliers
- Cung cấp feature importance
- Ổn định với nhiều loại dữ liệu

**Nhược điểm:**
- Khó diễn giải so với single tree
- Tốn bộ nhớ và thời gian training
- Có thể overfit với dữ liệu noise cao

### 2.3.4. Ứng dụng trong gợi ý cây trồng

Random Forest được sử dụng để:
- Đưa ra một khuyến nghị chính xác nhất
- Xử lý dữ liệu với 7 features cơ bản (N, P, K, temperature, humidity, pH, rainfall)
- Cung cấp confidence score cho dự đoán

## 2.4. Tiền xử lý dữ liệu

### 2.4.1. Feature Scaling

**StandardScaler:** Chuẩn hóa dữ liệu về phân phối chuẩn
```
x_scaled = (x - μ) / σ
```

**Lý do sử dụng:**
- KNN nhạy cảm với scale của features
- Đảm bảo các features có trọng số tương đương

### 2.4.2. Data Validation

**Missing Value Handling:**
- Kiểm tra và xử lý giá trị thiếu
- Sử dụng median/mode imputation khi cần

**Outlier Detection:**
- Sử dụng IQR method để phát hiện outliers
- Loại bỏ hoặc cap các giá trị bất thường

## 2.5. Kiến trúc phần mềm

### 2.5.1. Microservices Architecture

**Định nghĩa:** Kiến trúc chia ứng dụng thành các services nhỏ, độc lập [9].

**Ưu điểm trong dự án:**
- **Scalability:** Mỗi service có thể scale độc lập
- **Technology diversity:** KNN và RF service có thể sử dụng công nghệ khác nhau
- **Fault isolation:** Lỗi ở một service không ảnh hưởng toàn hệ thống
- **Development independence:** Teams có thể phát triển song song

### 2.5.2. Dependency Injection Pattern

**Khái niệm:** Pattern cung cấp dependencies từ bên ngoài thay vì tạo bên trong class [10].

**Ứng dụng trong FastAPI:**
```python
def get_model() -> KNNModel:
    return KNNModel(model_path, scaler_path, labels_path)

@app.post("/suggest")
async def suggest(model: KNNModel = Depends(get_model)):
    return model.predict(data)
```

**Lợi ích:**
- Dễ testing với mock objects
- Loose coupling giữa các components
- Dễ thay đổi implementation

### 2.5.3. Repository Pattern

**Mục đích:** Tách biệt logic truy cập dữ liệu khỏi business logic.

**Cấu trúc:**
```
Controller → Service → Repository → Data Source
```

## 2.6. Công nghệ Web

### 2.6.1. FastAPI Framework

**Đặc điểm chính:**
- **High performance:** Dựa trên Starlette và Pydantic
- **Type hints:** Automatic validation và documentation
- **Async support:** Xử lý concurrent requests hiệu quả

**Pydantic Models:**
```python
class CropRequest(BaseModel):
    temperature: float
    humidity: float
    ph: float
```

### 2.6.2. React Framework

**Component-based Architecture:**
- **Reusability:** Components có thể tái sử dụng
- **Maintainability:** Dễ bảo trì và debug
- **State management:** Quản lý state hiệu quả với hooks

**Virtual DOM:**
- Tối ưu hóa rendering performance
- Batch updates để giảm DOM manipulation

### 2.6.3. RESTful API Design

**Principles:**
- **Stateless:** Mỗi request độc lập
- **Resource-based:** URLs đại diện cho resources
- **HTTP methods:** GET, POST, PUT, DELETE có ý nghĩa rõ ràng

**API Endpoints trong dự án:**
```
POST /suggest - KNN suggestions
POST /rf/suggest - Random Forest suggestions
GET /rf/info - Model information
```

## 2.7. Đánh giá hiệu suất mô hình

### 2.7.1. Metrics đánh giá

**Accuracy:**
```
Accuracy = (TP + TN) / (TP + TN + FP + FN)
```

**Precision:**
```
Precision = TP / (TP + FP)
```

**Recall:**
```
Recall = TP / (TP + FN)
```

**F1-Score:**
```
F1 = 2 × (Precision × Recall) / (Precision + Recall)
```

### 2.7.2. Cross-Validation

**K-Fold Cross-Validation:**
- Chia dữ liệu thành k folds
- Train trên k-1 folds, test trên 1 fold
- Lặp k lần và tính trung bình kết quả

**Stratified Sampling:**
- Đảm bảo tỷ lệ các class trong mỗi fold
- Quan trọng với dữ liệu imbalanced

## 2.8. Kết luận chương

Chương này đã trình bày các cơ sở lý thuyết cần thiết cho việc xây dựng hệ thống gợi ý cây trồng:

1. **Machine Learning foundations:** Cung cấp nền tảng cho việc áp dụng AI
2. **KNN và Random Forest:** Hai thuật toán chính với đặc điểm phù hợp
3. **Software architecture:** Microservices và design patterns hiện đại
4. **Web technologies:** FastAPI và React cho full-stack development
5. **Evaluation methods:** Đảm bảo chất lượng và độ tin cậy

Những kiến thức này sẽ được áp dụng cụ thể trong Chương 3 để xây dựng hệ thống hoàn chỉnh.
