import { useState } from 'react'
import axios from 'axios'
import { toast } from 'react-toastify'

// API endpoint
const API_URL = 'http://localhost:8001/rf/suggest'

const RFCropSuggestionUI = () => {
  // State cho form input
  const [formData, setFormData] = useState({
    N: 50,
    P: 50,
    K: 50,
    temperature: 25,
    humidity: 70,
    ph: 6.5,
    rainfall: 100
  })

  // State cho kết quả
  const [results, setResults] = useState([])
  const [loading, setLoading] = useState(false)
  const [showResults, setShowResults] = useState(false)

  // Xử lý thay đổi input
  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData({
      ...formData,
      [name]: parseFloat(value)
    })
  }

  // Xử lý submit form
  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setShowResults(false)

    try {
      const response = await axios.post(API_URL, formData)
      setResults(response.data.suggestions)
      setShowResults(true)
      toast.success('Đ<PERSON> nhận được gợi ý cây trồng!')
    } catch (error) {
      console.error('Error fetching crop suggestions:', error)
      toast.error('Có lỗi xảy ra khi lấy gợi ý cây trồng')
    } finally {
      setLoading(false)
    }
  }



  return (
    <div className="container mx-auto p-4">
      <div className="bg-white shadow-md rounded-lg p-6 mb-8">
        <h2 className="text-2xl font-semibold mb-4">Nhập thông số môi trường</h2>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Nitrogen (N) */}
            <div>
              <label className="block text-gray-700 mb-2">
                Nitrogen (N): {formData.N} kg/ha
              </label>
              <div className="flex items-center gap-4">
                <input
                  type="range"
                  name="N"
                  min="0"
                  max="140"
                  value={formData.N}
                  onChange={handleInputChange}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
                <input
                  type="number"
                  name="N"
                  min="0"
                  max="140"
                  value={formData.N}
                  onChange={handleInputChange}
                  className="w-20 p-2 border border-gray-300 rounded-md text-center"
                />
              </div>
              <div className="flex justify-between text-xs text-gray-500">
                <span>0</span>
                <span>140 kg/ha</span>
              </div>
            </div>

            {/* Phosphorus (P) */}
            <div>
              <label className="block text-gray-700 mb-2">
                Phosphorus (P): {formData.P} kg/ha
              </label>
              <div className="flex items-center gap-4">
                <input
                  type="range"
                  name="P"
                  min="5"
                  max="145"
                  value={formData.P}
                  onChange={handleInputChange}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
                <input
                  type="number"
                  name="P"
                  min="5"
                  max="145"
                  value={formData.P}
                  onChange={handleInputChange}
                  className="w-20 p-2 border border-gray-300 rounded-md text-center"
                />
              </div>
              <div className="flex justify-between text-xs text-gray-500">
                <span>5</span>
                <span>145 kg/ha</span>
              </div>
            </div>

            {/* Potassium (K) */}
            <div>
              <label className="block text-gray-700 mb-2">
                Potassium (K): {formData.K} kg/ha
              </label>
              <div className="flex items-center gap-4">
                <input
                  type="range"
                  name="K"
                  min="5"
                  max="205"
                  value={formData.K}
                  onChange={handleInputChange}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
                <input
                  type="number"
                  name="K"
                  min="5"
                  max="205"
                  value={formData.K}
                  onChange={handleInputChange}
                  className="w-20 p-2 border border-gray-300 rounded-md text-center"
                />
              </div>
              <div className="flex justify-between text-xs text-gray-500">
                <span>5</span>
                <span>205 kg/ha</span>
              </div>
            </div>

            {/* Temperature */}
            <div>
              <label className="block text-gray-700 mb-2">
                Nhiệt độ: {formData.temperature}°C
              </label>
              <div className="flex items-center gap-4">
                <input
                  type="range"
                  name="temperature"
                  min="8"
                  max="44"
                  step="0.1"
                  value={formData.temperature}
                  onChange={handleInputChange}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
                <input
                  type="number"
                  name="temperature"
                  min="8"
                  max="44"
                  step="0.1"
                  value={formData.temperature}
                  onChange={handleInputChange}
                  className="w-20 p-2 border border-gray-300 rounded-md text-center"
                />
              </div>
              <div className="flex justify-between text-xs text-gray-500">
                <span>8°C</span>
                <span>44°C</span>
              </div>
            </div>

            {/* Humidity */}
            <div>
              <label className="block text-gray-700 mb-2">
                Độ ẩm: {formData.humidity}%
              </label>
              <div className="flex items-center gap-4">
                <input
                  type="range"
                  name="humidity"
                  min="14"
                  max="100"
                  step="0.1"
                  value={formData.humidity}
                  onChange={handleInputChange}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
                <input
                  type="number"
                  name="humidity"
                  min="14"
                  max="100"
                  step="0.1"
                  value={formData.humidity}
                  onChange={handleInputChange}
                  className="w-20 p-2 border border-gray-300 rounded-md text-center"
                />
              </div>
              <div className="flex justify-between text-xs text-gray-500">
                <span>14%</span>
                <span>100%</span>
              </div>
            </div>

            {/* pH */}
            <div>
              <label className="block text-gray-700 mb-2">
                pH: {formData.ph}
              </label>
              <div className="flex items-center gap-4">
                <input
                  type="range"
                  name="ph"
                  min="3.5"
                  max="10"
                  step="0.1"
                  value={formData.ph}
                  onChange={handleInputChange}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
                <input
                  type="number"
                  name="ph"
                  min="3.5"
                  max="10"
                  step="0.1"
                  value={formData.ph}
                  onChange={handleInputChange}
                  className="w-20 p-2 border border-gray-300 rounded-md text-center"
                />
              </div>
              <div className="flex justify-between text-xs text-gray-500">
                <span>3.5</span>
                <span>10</span>
              </div>
            </div>

            {/* Rainfall */}
            <div>
              <label className="block text-gray-700 mb-2">
                Lượng mưa: {formData.rainfall} mm
              </label>
              <div className="flex items-center gap-4">
                <input
                  type="range"
                  name="rainfall"
                  min="20"
                  max="300"
                  step="0.1"
                  value={formData.rainfall}
                  onChange={handleInputChange}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
                <input
                  type="number"
                  name="rainfall"
                  min="20"
                  max="300"
                  step="0.1"
                  value={formData.rainfall}
                  onChange={handleInputChange}
                  className="w-20 p-2 border border-gray-300 rounded-md text-center"
                />
              </div>
              <div className="flex justify-between text-xs text-gray-500">
                <span>20 mm</span>
                <span>300 mm</span>
              </div>
            </div>
          </div>

          <div className="flex justify-center mt-6">
            <button
              type="submit"
              className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-6 rounded-lg shadow-md transition duration-300 ease-in-out transform hover:scale-105"
              disabled={loading}
            >
              {loading ? 'Đang xử lý...' : 'Gợi ý cây trồng'}
            </button>
          </div>
        </form>
      </div>

      {/* Hiển thị kết quả - Chỉ hiển thị cây trồng phù hợp nhất */}
      {showResults && (
        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-2xl font-semibold mb-4">Cây trồng phù hợp nhất</h2>

          {results.length > 0 ? (
            <div>
              {/* Chỉ hiển thị cây trồng đầu tiên (phù hợp nhất) */}
              <div className="border rounded-lg p-6 hover:shadow-lg transition-shadow bg-green-50">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-2xl font-bold text-green-700">{results[0].name}</h3>

                </div>

                {/* Hiển thị ảnh cây trồng */}
                <div className="mt-4 flex justify-center">
                  <div className="relative w-full max-w-md overflow-hidden rounded-lg shadow-lg">
                    <img
                      src={`/images/${results[0].name}.jpg`}
                      alt={`Hình ảnh ${results[0].name}`}
                      className="w-full h-auto object-cover"
                      onError={(e) => {
                        e.target.onerror = null;
                        e.target.src = '/images/default.jpg';
                        console.log(`Không tìm thấy ảnh cho ${results[0].name}`);
                      }}
                    />
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
                      <h4 className="text-white font-semibold">{results[0].name}</h4>
                    </div>
                  </div>
                </div>



                <div className="mt-6 bg-green-100 p-4 rounded-lg">
                  <h4 className="font-semibold text-green-800 mb-2">Thông tin bổ sung</h4>
                  <p className="text-green-700">
                    Mô hình Random Forest đã xác định đây là loại cây trồng phù hợp nhất với các điều kiện môi trường bạn đã nhập.
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <p className="text-gray-600">Không tìm thấy gợi ý cây trồng phù hợp.</p>
          )}
        </div>
      )}
    </div>
  )
}

export default RFCropSuggestionUI