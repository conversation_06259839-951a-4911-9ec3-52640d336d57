import React, { useState } from 'react';
import { Card, CardContent, CardHeader } from "./ui/card";
import { Input } from "./ui/input";
import { Button } from "./ui/button";
import { Checkbox } from "./ui/checkbox";
import { RadioGroup, RadioGroupItem } from "./ui/radio-group";
import { Label } from "./ui/label";

// Ánh xạ giá trị cho các lựa chọn độ sâu của đất
const depthMap = {
  'Rất nông (<20 cm)': 0,    // Rất nông (<20 cm)
  'Nông (20-50 cm)': 1,       // Nông (20-50 cm)
  'Trung bình (50-150 cm)': 2,       // Trung bình (50-150 cm)
  'Sâu (>150 cm)': 3           // Sâu (>150 cm)
};

// Ánh xạ giá trị cho các mức độ độ phì nhiêu của đất
const fertilityMap = {
  'Thấp': 0,      // Thấp
  'Trung bình': 1,  // Trung bình
  'Cao': 2       // <PERSON>
};

// Ánh xạ giá trị cho các mức độ độ mặn của đất
const salinityMap = {
  'Không mặn': 0,               // Không mặn
  'Mặn thấp (<4 dS/m)': 1,     // Mặn thấp (<4 dS/m)
  'Mặn trung bình (4-10 dS/m)': 2, // Mặn trung bình (4-10 dS/m)
  'Mặn cao (>10 dS/m)': 3     // Mặn cao (>10 dS/m)
};

// Định nghĩa các nhóm thuộc tính có thể chọn nhiều giá trị
const multiSelectGroups = [
  {
    key: 'texture_ops',    // Kết cấu đất tối ưu
    label: 'Kết cấu đất tối ưu',
    options: ['heavy', 'light', 'medium', 'organic', 'wide']  // nặng, nhẹ, trung bình, hữu cơ, rộng
  },
  {
    key: 'texture_abs',    // Kết cấu đất tuyệt đối
    label: 'Kết cấu đất giới hạn',
    options: ['heavy', 'light', 'medium', 'organic', 'wide']
  },
  {
    key: 'drainage_opt',   // Khả năng thoát nước tối ưu
    label: 'Khả năng thoát nước tối ưu',
    options: [
      'Quá mức (khô/hơi khô)',       // Quá mức (khô/hơi khô)
      'Kém (ngập >50% thời gian trong năm)',      // Kém (ngập >50% thời gian trong năm)
      'Tốt (có các đợt khô)'                     // Tốt (có các đợt khô)
    ]
  },
  {
    key: 'drainage_abs',   // Khả năng thoát nước tuyệt đối
    label: 'Khả năng thoát nước giới hạn',
    options: [
      'Quá mức (khô/hơi khô)',
      'Kém (ngập >50% thời gian trong năm)',
      'Tốt (có các đợt khô)'
    ]
  },
  {
    key: 'climate_zone',   // Vùng khí hậu
    label: 'Vùng khí hậu',
    options: [
      'Hàn đới (E)',                          // Hàn đới (E)
      'Sa mạc hoặc khô hạn (Bw)',                 // Sa mạc hoặc khô hạn (Bw)
      'Thảo nguyên hoặc bán khô hạn (Bs)',             // Thảo nguyên hoặc bán khô hạn (Bs)
      'Cận nhiệt đới khô hạn mùa hè (Cs)',          // Cận nhiệt đới khô hạn mùa hè (Cs)
      'Cận nhiệt đới khô hạn mùa đông (Cw)',          // Cận nhiệt đới khô hạn mùa đông (Cw)
      'Cận nhiệt đới ẩm (Cf)',               // Cận nhiệt đới ẩm (Cf)
      'Ôn đới lục địa (Dc)',           // Ôn đới lục địa (Dc)
      'Ôn đới hải dương (Do)',               // Ôn đới hải dương (Do)
      'Ôn đới khô hạn mùa đông (Dw)',      // Ôn đới khô hạn mùa đông (Dw)
      'Ôn đới ẩm ướt mùa đông (Df)',    // Ôn đới ẩm ướt mùa đông (Df)
      'Nhiệt đới mưa và khô (Aw)',              // Nhiệt đới mưa và khô (Aw)
      'Nhiệt đới ẩm (Ar)'                     // Nhiệt đới ẩm (Ar)
    ]
  },
  {
    key: 'photoperiod',    // Quang kỳ
    label: 'Quang kỳ',
    options: [
      'long day (>14 hours)',                // Ngày dài (>14 giờ)
      'neutral day (12-14 hours)',           // Ngày trung tính (12-14 giờ)
      'not sensitive',                       // Không nhạy cảm
      'short day (<12 hours)'                // Ngày ngắn (<12 giờ)
    ]
  }
];

export default function KNNPage() {
  // Khởi tạo trạng thái form với giá trị mặc định
  const initialForm = {
    temp_opt_min: '',      // Nhiệt độ tối thiểu tối ưu
    temp_opt_max: '',      // Nhiệt độ tối đa tối ưu
    rain_opt_min: '',      // Lượng mưa tối thiểu tối ưu
    rain_opt_max: '',      // Lượng mưa tối đa tối ưu
    ph_opt_min: '',        // pH tối thiểu tối ưu
    ph_opt_max: '',        // pH tối đa tối ưu
    depth_opt: '',         // Độ sâu đất tối ưu
    fertility_ops: '',     // Độ phì nhiêu tối ưu
    salinity_ops: '',      // Độ mặn tối ưu
  };

  // Khởi tạo các nhóm chọn nhiều với đối tượng rỗng
  multiSelectGroups.forEach(group => {
    initialForm[group.key] = {};
  });

  // Các state của component
  const [form, setForm] = useState(initialForm);          // Lưu trữ dữ liệu form
  const [loading, setLoading] = useState(false);          // Trạng thái đang xử lý
  const [result, setResult] = useState([]);               // Kết quả gợi ý cây trồng
  const [error, setError] = useState(null);               // Thông báo lỗi

  // Xử lý sự kiện thay đổi giá trị trong form
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    if (type === 'checkbox') {
      // Xử lý cho các trường checkbox
      setForm(prev => ({
        ...prev,
        [name]: { ...prev[name], [value]: checked }
      }));
    } else {
      // Xử lý cho các trường input thông thường
      setForm(prev => ({ ...prev, [name]: value }));
    }
  };

  // Xây dựng dữ liệu gửi lên server
  const buildPayload = (formData) => {
    // Khởi tạo với các trường số
    const payload = {
      temp_opt_min: parseFloat(formData.temp_opt_min) || 0,
      temp_opt_max: parseFloat(formData.temp_opt_max) || 0,
      rain_opt_min: parseFloat(formData.rain_opt_min) || 0,
      rain_opt_max: parseFloat(formData.rain_opt_max) || 0,
      ph_opt_min: parseFloat(formData.ph_opt_min) || 0,
      ph_opt_max: parseFloat(formData.ph_opt_max) || 0,
      // Chuyển đổi các trường categorical sang số
      depth_opt: parseInt(formData.depth_opt) || 0,
      fertility_ops: parseInt(formData.fertility_ops) || 0,
      salinity_ops: parseInt(formData.salinity_ops) || 0,
    };

    // Chuyển đổi các trường checkbox thành các trường boolean (0/1)
    // Cho kết cấu đất tối ưu
    payload['texture_ops_heavy'] = formData.texture_ops?.heavy ? 1 : 0;
    payload['texture_ops_light'] = formData.texture_ops?.light ? 1 : 0;
    payload['texture_ops_medium'] = formData.texture_ops?.medium ? 1 : 0;
    payload['texture_ops_organic'] = formData.texture_ops?.organic ? 1 : 0;
    payload['texture_ops_wide'] = formData.texture_ops?.wide ? 1 : 0;

    // Cho kết cấu đất tuyệt đối
    payload['texture_abs_heavy'] = formData.texture_abs?.heavy ? 1 : 0;
    payload['texture_abs_light'] = formData.texture_abs?.light ? 1 : 0;
    payload['texture_abs_medium'] = formData.texture_abs?.medium ? 1 : 0;
    payload['texture_abs_organic'] = formData.texture_abs?.organic ? 1 : 0;
    payload['texture_abs_wide'] = formData.texture_abs?.wide ? 1 : 0;

    // Cho khả năng thoát nước tối ưu
    payload['drainage_opt_excessive (dry/moderately dry)'] = formData.drainage_opt?.['excessive (dry/moderately dry)'] ? 1 : 0;
    payload['drainage_opt_poorly (saturated >50% of year)'] = formData.drainage_opt?.['poorly (saturated >50% of year)'] ? 1 : 0;
    payload['drainage_opt_well (dry spells)'] = formData.drainage_opt?.['well (dry spells)'] ? 1 : 0;

    // Cho khả năng thoát nước tuyệt đối
    payload['drainage_abs_excessive (dry/moderately dry)'] = formData.drainage_abs?.['excessive (dry/moderately dry)'] ? 1 : 0;
    payload['drainage_abs_poorly (saturated >50% of year)'] = formData.drainage_abs?.['poorly (saturated >50% of year)'] ? 1 : 0;
    payload['drainage_abs_well (dry spells)'] = formData.drainage_abs?.['well (dry spells)'] ? 1 : 0;

    // Cho vùng khí hậu
    const climateZones = [
      'boreal (E)',
      'desert or arid (Bw)',
      'steppe or semiarid (Bs)',
      'subtropical dry summer (Cs)',
      'subtropical dry winter (Cw)',
      'subtropical humid (Cf)',
      'temperate continental (Dc)',
      'temperate oceanic (Do)',
      'temperate with dry winters (Dw)',
      'temperate with humid winters (Df)',
      'tropical wet & dry (Aw)',
      'tropical wet (Ar)'
    ];

    climateZones.forEach(zone => {
      payload[`climate_zone_${zone}`] = formData.climate_zone?.[zone] ? 1 : 0;
    });

    // Cho quang kỳ
    payload['photoperiod_long day (>14 hours)'] = formData.photoperiod?.['long day (>14 hours)'] ? 1 : 0;
    payload['photoperiod_neutral day (12-14 hours)'] = formData.photoperiod?.['neutral day (12-14 hours)'] ? 1 : 0;
    payload['photoperiod_not sensitive'] = formData.photoperiod?.['not sensitive'] ? 1 : 0;
    payload['photoperiod_short day (<12 hours)'] = formData.photoperiod?.['short day (<12 hours)'] ? 1 : 0;

    return payload;
  };

  // Xử lý sự kiện submit form
  const handleSubmit = async () => {
    try {
      setLoading(true);
      setError(null);
      const payload = buildPayload(form);

      // Gửi request đến API với timeout và better error handling
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10s timeout

      try {
        const response = await fetch('http://localhost:8000/suggest', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          body: JSON.stringify(payload),
          signal: controller.signal,
          credentials: 'omit' // Disable credentials for CORS
        });

        clearTimeout(timeoutId);
        const data = await response.json();

        // Xử lý lỗi từ API
        if (!response.ok) {
          if (response.status === 422 && data.detail) {
            const errorDetails = Array.isArray(data.detail) ?
              data.detail.map(err => `${err.loc.join('.')}: ${err.msg}`).join('\n') :
              data.detail;
            throw new Error(errorDetails);
          }
          throw new Error(data.detail || 'Có lỗi xảy ra khi xử lý yêu cầu');
        }

        // Cập nhật kết quả
        setResult(data.suggestions || []);
      } catch (fetchError) {
        if (fetchError.name === 'AbortError') {
          throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối và thử lại.');
        }
        throw fetchError;
      }
    } catch (err) {
      setError(err.message);
      console.error('Lỗi:', err);
    } finally {
      setLoading(false);
    }
  };

  // Render giao diện
  return (
    <Card className="p-6 grid gap-6 w-full max-w-5xl mx-auto shadow-xl border border-green-100">
      <CardHeader className="pb-2 text-center">
        <h2 className="text-2xl font-bold text-green-800 flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mr-2 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
          </svg>
          Hệ thống gợi ý cây trồng sử dụng KNN
        </h2>
        <p className="text-gray-600 mt-2">Nhập thông số môi trường để nhận gợi ý về các loại cây trồng phù hợp</p>
      </CardHeader>

      <CardContent className="grid gap-8">
        {/* Phần nhập liệu số */}
        <div className="bg-green-50 p-6 rounded-lg border border-green-100 shadow-sm">
          <h3 className="text-lg font-medium mb-4 text-green-800 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
            </svg>
            Thông số nhiệt độ, lượng mưa, pH đất
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="space-y-2">
              <Label htmlFor="temp_opt_min" className="text-sm font-medium text-gray-700 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10a1 1 0 011-1h12a1 1 0 110 2H6a1 1 0 01-1-1z M12 19V5" />
                </svg>
                Nhiệt độ tối thiểu (°C)
              </Label>
              <Input
                id="temp_opt_min"
                name="temp_opt_min"
                type="number"
                value={form.temp_opt_min}
                onChange={handleChange}
                className="focus:ring-green-500 focus:border-green-500"
                placeholder="Ví dụ: 15"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="temp_opt_max" className="text-sm font-medium text-gray-700 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Nhiệt độ tối đa (°C)
              </Label>
              <Input
                id="temp_opt_max"
                name="temp_opt_max"
                type="number"
                value={form.temp_opt_max}
                onChange={handleChange}
                className="focus:ring-green-500 focus:border-green-500"
                placeholder="Ví dụ: 30"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="rain_opt_min" className="text-sm font-medium text-gray-700 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                </svg>
                Lượng mưa tối thiểu (mm)
              </Label>
              <Input
                id="rain_opt_min"
                name="rain_opt_min"
                type="number"
                value={form.rain_opt_min}
                onChange={handleChange}
                className="focus:ring-green-500 focus:border-green-500"
                placeholder="Ví dụ: 500"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="rain_opt_max" className="text-sm font-medium text-gray-700 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                </svg>
                Lượng mưa tối đa (mm)
              </Label>
              <Input
                id="rain_opt_max"
                name="rain_opt_max"
                type="number"
                value={form.rain_opt_max}
                onChange={handleChange}
                className="focus:ring-green-500 focus:border-green-500"
                placeholder="Ví dụ: 2000"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="ph_opt_min" className="text-sm font-medium text-gray-700 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                </svg>
                pH tối thiểu
              </Label>
              <Input
                id="ph_opt_min"
                name="ph_opt_min"
                type="number"
                step="0.1"
                value={form.ph_opt_min}
                onChange={handleChange}
                className="focus:ring-green-500 focus:border-green-500"
                placeholder="Ví dụ: 5.5"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="ph_opt_max" className="text-sm font-medium text-gray-700 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-purple-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                pH tối đa
              </Label>
              <Input
                id="ph_opt_max"
                name="ph_opt_max"
                type="number"
                step="0.1"
                value={form.ph_opt_max}
                onChange={handleChange}
                className="focus:ring-green-500 focus:border-green-500"
                placeholder="Ví dụ: 7.5"
              />
            </div>
          </div>
        </div>

        {/* Phần lựa chọn radio */}
        <div className="bg-blue-50 p-6 rounded-lg border border-blue-100 shadow-sm">
          <h3 className="text-lg font-medium mb-4 text-blue-800 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            Đặc điểm đất
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white p-4 rounded-lg shadow-sm border border-blue-100">
              <div className="space-y-3">
                <Label className="text-blue-700 font-medium flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                  Độ sâu đất
                </Label>
                <RadioGroup
                  value={String(form.depth_opt)}
                  onValueChange={(value) => setForm(prev => ({ ...prev, depth_opt: value }))}
                  className="space-y-2"
                >
                  {Object.entries(depthMap).map(([label, value]) => (
                    <div key={value} className="flex items-center space-x-2 p-2 rounded hover:bg-blue-50 transition-colors">
                      <RadioGroupItem value={String(value)} id={`depth-${value}`} className="text-blue-600" />
                      <Label htmlFor={`depth-${value}`} className="text-sm cursor-pointer">{label}</Label>
                    </div>
                  ))}
                </RadioGroup>
              </div>
            </div>

            <div className="bg-white p-4 rounded-lg shadow-sm border border-blue-100">
              <div className="space-y-3">
                <Label className="text-green-700 font-medium flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                  </svg>
                  Độ phì nhiêu
                </Label>
                <RadioGroup
                  value={String(form.fertility_ops)}
                  onValueChange={(value) => setForm(prev => ({ ...prev, fertility_ops: value }))}
                  className="space-y-2"
                >
                  {Object.entries(fertilityMap).map(([label, value]) => (
                    <div key={value} className="flex items-center space-x-2 p-2 rounded hover:bg-green-50 transition-colors">
                      <RadioGroupItem value={String(value)} id={`fertility-${value}`} className="text-green-600" />
                      <Label htmlFor={`fertility-${value}`} className="text-sm cursor-pointer">{label}</Label>
                    </div>
                  ))}
                </RadioGroup>
              </div>
            </div>

            <div className="bg-white p-4 rounded-lg shadow-sm border border-blue-100">
              <div className="space-y-3">
                <Label className="text-cyan-700 font-medium flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                  </svg>
                  Độ mặn
                </Label>
                <RadioGroup
                  value={String(form.salinity_ops)}
                  onValueChange={(value) => setForm(prev => ({ ...prev, salinity_ops: value }))}
                  className="space-y-2"
                >
                  {Object.entries(salinityMap).map(([label, value]) => (
                    <div key={value} className="flex items-center space-x-2 p-2 rounded hover:bg-cyan-50 transition-colors">
                      <RadioGroupItem value={String(value)} id={`salinity-${value}`} className="text-cyan-600" />
                      <Label htmlFor={`salinity-${value}`} className="text-sm cursor-pointer">{label}</Label>
                    </div>
                  ))}
                </RadioGroup>
              </div>
            </div>
          </div>
        </div>

        {/* Phần lựa chọn nhiều */}
        <div className="bg-purple-50 p-6 rounded-lg border border-purple-100 shadow-sm">
          <h3 className="text-lg font-medium mb-4 text-purple-800 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
            Khả năng thoát nước và kiểu khí hậu
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {multiSelectGroups.map(group => (
              <div key={group.key} className="bg-white p-4 rounded-lg shadow-sm border border-purple-100">
                <div className="space-y-3">
                  <Label className="text-purple-700 font-medium flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                    {group.label}
                  </Label>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-1">
                    {group.options.map(option => (
                      <div key={option} className="flex items-center space-x-2 p-2 rounded hover:bg-purple-50 transition-colors">
                        <Checkbox
                          id={`${group.key}-${option}`}
                          name={group.key}
                          value={option}
                          checked={form[group.key][option] || false}
                          className="text-purple-600 rounded-sm"
                          onCheckedChange={(checked) =>
                            handleChange({
                              target: {
                                name: group.key,
                                value: option,
                                type: 'checkbox',
                                checked
                              }
                            })
                          }
                        />
                        <Label htmlFor={`${group.key}-${option}`} className="text-sm cursor-pointer">
                          {option.replace(/_/g, ' ').replace(/\b\w/g, c => c.toUpperCase())}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Nút gửi form */}
        <div className="flex justify-center">
          <Button
            onClick={handleSubmit}
            disabled={loading}
            className="bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white px-8 py-6 rounded-lg text-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2"
          >
            {loading ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>Đang xử lý...</span>
              </>
            ) : (
              <>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                </svg>
                <span>Gợi ý cây trồng</span>
              </>
            )}
          </Button>
        </div>

        {/* Thông báo lỗi */}
        {error && (
          <div className="p-6 border-2 border-red-500 rounded-lg bg-red-50 text-red-700 whitespace-pre-line shadow-md">
            <div className="flex items-center mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h3 className="text-lg font-medium text-red-700">Đã xảy ra lỗi</h3>
            </div>
            <p>{error}</p>
          </div>
        )}

        {/* Kết quả */}
        {result.length > 0 && (
          <div className="mt-8 space-y-6 bg-amber-50 p-6 rounded-lg border border-amber-200 shadow-md">
            <h3 className="text-xl font-bold text-amber-800 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2 text-amber-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
              Top 5 cây trồng được gợi ý
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {result.map((crop, index) => (
                <Card key={index} className="overflow-hidden border border-amber-200 shadow-lg hover:shadow-xl transition-shadow duration-300">
                  <div className="bg-gradient-to-r from-amber-500 to-amber-400 p-4">
                    <h3 className="text-lg font-bold text-white">{crop.species_name || crop.species_code}</h3>
                  </div>
                  <div className="p-4 space-y-3 text-sm">
                    {crop.life_form && (
                      <div className="flex items-start">
                        <span className="font-semibold text-amber-800 min-w-[120px]">Dạng sống:</span>
                        <span className="text-gray-700">{crop.life_form}</span>
                      </div>
                    )}
                    {crop.habit && (
                      <div className="flex items-start">
                        <span className="font-semibold text-amber-800 min-w-[120px]">Thói quen:</span>
                        <span className="text-gray-700">{crop.habit}</span>
                      </div>
                    )}
                    {crop.life_span && (
                      <div className="flex items-start">
                        <span className="font-semibold text-amber-800 min-w-[120px]">Tuổi thọ:</span>
                        <span className="text-gray-700">{crop.life_span}</span>
                      </div>
                    )}
                    {crop.physiology && (
                      <div className="flex items-start">
                        <span className="font-semibold text-amber-800 min-w-[120px]">Sinh lý học:</span>
                        <span className="text-gray-700">{crop.physiology}</span>
                      </div>
                    )}
                    {crop.category && (
                      <div className="flex items-start">
                        <span className="font-semibold text-amber-800 min-w-[120px]">Danh mục:</span>
                        <span className="text-gray-700">{crop.category}</span>
                      </div>
                    )}
                    {crop.plant_attributes && (
                      <div className="flex items-start">
                        <span className="font-semibold text-amber-800 min-w-[120px]">Thuộc tính:</span>
                        <span className="text-gray-700">{crop.plant_attributes}</span>
                      </div>
                    )}
                    {crop.growing_cycle && (
                      <div className="flex items-start">
                        <span className="font-semibold text-amber-800 min-w-[120px]">Chu kỳ phát triển:</span>
                        <span className="text-gray-700">{crop.growing_cycle}</span>
                      </div>
                    )}
                    {crop.main_use && (
                      <div className="flex items-start">
                        <span className="font-semibold text-amber-800 min-w-[120px]">Công dụng chính:</span>
                        <span className="text-gray-700">{crop.main_use}</span>
                      </div>
                    )}
                    {crop.detailed_use && (
                      <div className="flex items-start">
                        <span className="font-semibold text-amber-800 min-w-[120px]">Công dụng chi tiết:</span>
                        <span className="text-gray-700">{crop.detailed_use}</span>
                      </div>
                    )}
                    {crop.used_part && (
                      <div className="flex items-start">
                        <span className="font-semibold text-amber-800 min-w-[120px]">Bộ phận sử dụng:</span>
                        <span className="text-gray-700">{crop.used_part}</span>
                      </div>
                    )}
                  </div>
                </Card>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
