from pydantic import BaseModel
from typing import List, Dict, Any, Optional

class CropInfo(BaseModel):
    """
    Model cho thông tin cây trồng
    """
    id: int
    species_name: str = "Unknown"
    life_form: Optional[str] = None
    habit: Optional[str] = None
    life_span: Optional[str] = None
    physiology: Optional[str] = None
    category: Optional[str] = None
    plant_attributes: Optional[str] = None
    growing_cycle: Optional[str] = None
    main_use: Optional[str] = None
    detailed_use: Optional[str] = None
    used_part: Optional[str] = None
    min_growing_cycle: int = 0
    max_growing_cycle: int = 0

class CropResponse(BaseModel):
    """
    Model cho response gợi ý cây trồng
    """
    suggestions: List[Dict[str, Any]]

    class Config:
        from_attributes = True

class RFCropInfo(BaseModel):
    """
    Model cho thông tin cây trồng từ Random Forest
    """
    id: int
    name: str
    probability: float
    details: Optional[Dict[str, Any]] = None

class RFCropResponse(BaseModel):
    """
    Model cho response gợi ý cây trồng từ Random Forest
    """
    suggestions: List[RFCropInfo]

    class Config:
        from_attributes = True
