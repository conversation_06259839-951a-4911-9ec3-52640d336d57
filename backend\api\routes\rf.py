from fastapi import APIRouter, Depends, HTTPException
from typing import Dict, Any
from ...core.logging import logger
from ...core.exceptions import convert_exception_to_http_exception
from ...services.suggestion_service import SuggestionService
from ..dependencies import get_rf_suggestion_service
from ..models.requests import RFCropRequest
from ..models.responses import RFCropResponse

router = APIRouter(tags=["Random Forest"])

@router.post('/suggest', response_model=RFCropResponse)
async def suggest_rf(
    req: RFCropRequest,
    suggestion_service: SuggestionService = Depends(get_rf_suggestion_service)
):
    """
    API endpoint để gợi ý cây trồng sử dụng mô hình Random Forest.

    Args:
        request: Đ<PERSON>i tượng RFCropRequest chứa các thông số đầu vào
        suggestion_service: Dịch vụ gợi ý cây trồng

    Returns:
        RFCropResponse: Đ<PERSON>i tượng chứa danh sách các cây trồng được gợi ý

    Raises:
        HTTPException: Nếu có lỗi xảy ra trong quá trình xử lý
    """
    try:
        logger.info(f"Nhận yêu cầu gợi ý cây trồng với dữ liệu: {req}")

        # Chuyển đổi request thành dictionary
        input_data = req.model_dump()

        # Gọi dịch vụ gợi ý
        suggestions = suggestion_service.get_suggestions(input_data)

        # Trả về kết quả
        return RFCropResponse(suggestions=suggestions)

    except Exception as e:
        logger.error(f"Lỗi khi xử lý yêu cầu: {str(e)}")
        raise convert_exception_to_http_exception(e)

@router.get('/info')
async def get_model_info():
    """
    Trả về thông tin về mô hình Random Forest.

    Returns:
        Dict: Thông tin về mô hình
    """
    return {
        "model_name": "Random Forest Classifier",
        "description": "Mô hình Random Forest được huấn luyện để gợi ý cây trồng dựa trên các điều kiện môi trường",
        "input_features": ["N", "P", "K", "temperature", "humidity", "ph", "rainfall"],
        "version": "1.0.0"
    }
