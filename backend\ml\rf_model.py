import joblib
import pandas as pd
from typing import Dict, Any, List
from .base_model import BaseModel
from ..core.logging import logger
from ..core.exceptions import ModelNotFoundException, PredictionException, InvalidInputException

class RandomForestModel(BaseModel):
    """
    Mô hình Random Forest
    """
    def __init__(self, model_path: str):
        """
        Khởi tạo mô hình Random Forest

        Args:
            model_path: Đường dẫn đến file model Random Forest
        """
        super().__init__(model_path)
        # Các cột đầu vào mong đợi cho mô hình Random Forest
        self.expected_columns = ['N', 'P', 'K', 'temperature', 'humidity', 'ph', 'rainfall']

    def predict(self, features: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Dự đoán dựa trên đặc trưng đầu vào

        Args:
            features: Đặc trưng đầu vào

        Returns:
            Danh sách thông tin cây trồng được gợi ý kèm xác suất

        Raises:
            InvalidInputException: Nếu dữ liệu đầu vào không hợp lệ
            PredictionException: Nếu có lỗi trong quá trình dự đoán
        """
        try:
            logger.info("Đang xử lý dữ liệu đầu vào cho Random Forest")

            # Tạo DataFrame từ dữ liệu đầu vào
            input_df = pd.DataFrame([features])

            # Đảm bảo thứ tự các cột phù hợp với mô hình
            for col in self.expected_columns:
                if col not in input_df.columns:
                    raise InvalidInputException(f"Thiếu trường dữ liệu: {col}")

            # Sắp xếp lại các cột theo thứ tự mong đợi
            input_df = input_df[self.expected_columns]

            # Dự đoán xác suất cho mỗi lớp
            probabilities = self.model.predict_proba(input_df)[0]

            # Lấy tên các lớp (cây trồng)
            class_names = self.model.classes_
            logger.info(f"Các lớp từ mô hình: {class_names}")

            # Dự đoán trực tiếp
            prediction = self.model.predict(input_df)[0]
            logger.info(f"Dự đoán trực tiếp: {prediction}")

            # Ánh xạ từ số nguyên sang tên cây trồng
            crop_mapping = {
                0: "rice",
                1: "maize",
                2: "chickpea",
                3: "kidneybeans",
                4: "pigeonpeas",
                5: "mothbeans",
                6: "mungbean",
                7: "blackgram",
                8: "lentil",
                9: "pomegranate",
                10: "banana",
                11: "mango",
                12: "grapes",
                13: "watermelon",
                14: "muskmelon",
                15: "apple",
                16: "orange",
                17: "papaya",
                18: "coconut",
                19: "cotton",
                20: "jute",
                21: "coffee"
            }

            # Tạo danh sách kết quả và sắp xếp theo xác suất giảm dần
            results = []
            for i, (crop_name, prob) in enumerate(zip(class_names, probabilities)):
                crop_id = i + 1  # ID tạm thời
                # Lấy tên cây trồng từ ánh xạ nếu có
                try:
                    crop_index = int(crop_name)
                    crop_display_name = crop_mapping.get(crop_index, f"unknown_{crop_index}")
                except (ValueError, TypeError):
                    crop_display_name = str(crop_name)

                logger.info(f"Cây trồng {i}: {crop_name} -> {crop_display_name} (xác suất: {prob})")

                results.append({
                    "id": crop_id,
                    "name": crop_display_name,  # Sử dụng tên cây trồng thực tế
                    "probability": float(prob)
                })

            # Sắp xếp kết quả theo xác suất giảm dần và chỉ lấy cây phù hợp nhất
            results.sort(key=lambda x: x["probability"], reverse=True)
            best_result = results[:1]

            logger.info(f"Đã dự đoán cây trồng phù hợp nhất: {best_result}")

            return best_result

        except InvalidInputException as e:
            logger.error(f"Lỗi dữ liệu đầu vào: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Lỗi khi dự đoán: {str(e)}")
            raise PredictionException(f"Lỗi khi dự đoán: {str(e)}")
