TÓM TẮT CHUYÊN ĐỀ
Trong bối cảnh giao thông ngày càng phức tạp và số lượng phương tiện tham gia lưu thông không ngừng gia tăng, việc đảm bảo an toàn giao thông trở thành một yêu cầu cấp thiết. Một trong những yếu tố then chốt giúp giảm thiểu tai nạn là nhận diện và tuân thủ đúng các biển báo giao thông. <PERSON><PERSON>, trong thực tế, người tham gia giao thông đôi khi bỏ sót hoặc không kịp nhận diện các biển báo, đặc biệt trong điều kiện tầm nhìn hạn chế. Chính vì vậy, việc nghiên cứu và xây dựng một hệ thống phát hiện biển báo giao thông tự động dựa trên thị gi<PERSON>c máy tính là hết sức cần thiết.
Chu<PERSON><PERSON>n đề này tập trung vào việc xây dựng mô hình phát hiện biển báo giao thông Việt Nam sử dụng kiến trúc YOLO11n – một phiên bản tối ưu và nhẹ của dòng mô hình YOLO hiện đại. Dữ liệu huấn luyện được lấy từ bộ dữ liệu Vietnamese Traffic Signs trên Kaggle, bao gồm hàng nghìn hình ảnh được gán nhãn theo định dạng YOLO. Quy trình chuẩn bị dữ liệu bao gồm các bước: tiền xử lý, kiểm tra – làm sạch dữ liệu, cân bằng dữ liệu bằng SMOTE, chia tập train/val/test và áp dụng các kỹ thuật tăng cường dữ liệu để huấn luyện mô hình.
Mô hình được huấn luyện trên GPU RTX 3060 với các cấu hình khác nhau nhằm đánh giá hiệu suất. Kết quả huấn luyện cho thấy mô hình YOLOv11n đạt độ chính xác cao với mAP@0.5 lên đến 93.1%, mAP@0.5:0.95 đạt 70.8%, Precision 91.8% và Recall 87.5%. Ngoài ra, chuyên đề cũng thực hiện so sánh kết quả giữa YOLOv11n và YOLOv8n, từ đó đánh giá hiệu quả và tiềm năng ứng dụng thực tế của từng mô hình.
Kết luận, mô hình YOLOv11n thể hiện sự hiệu quả trong việc phát hiện và nhận diện biển báo giao thông Việt Nam, đặc biệt phù hợp với các ứng dụng thời gian thực và môi trường hạn chế tài nguyên như hệ thống camera giao thông, thiết bị IoT, hoặc xe tự hành. Chuyên đề đồng thời đề xuất hướng phát triển mở rộng dữ liệu, thử nghiệm huấn luyện với các phiên bản YOLO mạnh hơn và tích hợp vào ứng dụng thực tiễn trong tương lai.
