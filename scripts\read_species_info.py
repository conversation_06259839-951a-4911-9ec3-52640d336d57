import pandas as pd

def read_species_info():
    # Read both CSV files
    merged_species = pd.read_csv('../data/merged_species.csv')
    crop_env = pd.read_csv('../data/crop_env_features_no_abs - crop_env_features_no_abs.csv')

    # Get unique species from crop_env file
    unique_species = crop_env['species_label'].unique()

    # Define columns to display
    columns_to_display = [
        'species_code', 'Life.form', 'Habit', 'Life.span', 'Physiology',
        'Category', 'Plant.attributes', 'cycle_min', 'cycle_max',
        'use.main', 'use.detailed', 'use.part'
    ]

    # Filter merged_species data for the species in crop_env
    species_info = merged_species[
        merged_species['species_code'].isin(unique_species)
    ][columns_to_display]

    # Display the information
    print("\nSpecies Information:")
    print("=" * 80)
    for _, row in species_info.iterrows():
        print("\nSpecies Code:", row['species_code'])
        for col in columns_to_display[1:]:  # Skip species_code as it's already printed
            print(f"{col}: {row[col]}")
        print("-" * 40)

if __name__ == "__main__":
    read_species_info()