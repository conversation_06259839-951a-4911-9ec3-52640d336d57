{"name": "crop-suggestion-frontend", "version": "0.1.0", "private": true, "dependencies": {"@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-slot": "^1.0.2", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.5.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "lucide-react": "^0.279.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-toastify": "^11.0.5", "tailwind-merge": "^1.14.0", "tailwindcss": "^3.3.3", "tailwindcss-animate": "^1.0.7", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8000"}