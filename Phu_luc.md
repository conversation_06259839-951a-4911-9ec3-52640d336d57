# PHỤ LỤC

## Ph<PERSON> lục A: <PERSON><PERSON><PERSON> trúc dữ liệu

### A.1. <PERSON><PERSON><PERSON> trúc <PERSON>_recommendation.csv

| Cột | Kiểu dữ liệu | <PERSON><PERSON> tả | Đơn vị | Phạm vi giá trị |
|-----|--------------|-------|--------|-----------------|
| N | float | Hàm lượng Nitrogen | kg/ha | 0-140 |
| P | float | Hàm lượng Phosphorus | kg/ha | 5-145 |
| K | float | Hàm lượng Potassium | kg/ha | 5-205 |
| temperature | float | Nhiệt độ trung bình | °C | 8.8-43.7 |
| humidity | float | Độ ẩm tương đối | % | 14.3-99.9 |
| ph | float | Độ pH của đất | - | 3.5-9.9 |
| rainfall | float | Lượng mưa trung bình | mm | 20.2-298.6 |
| label | string | Tê<PERSON> cây trồng | - | 22 loại cây |

### A.2. <PERSON><PERSON> sách 22 loại cây trồng

1. rice (lúa)
2. maize (ngô)
3. chickpea (đậu gà)
4. kidneybeans (đậu thận)
5. pigeonpeas (đậu chim)
6. mothbeans (đậu ngài)
7. mungbean (đậu xanh)
8. blackgram (đậu đen)
9. lentil (đậu lăng)
10. pomegranate (lựu)
11. banana (chuối)
12. mango (xoài)
13. grapes (nho)
14. watermelon (dưa hấu)
15. muskmelon (dưa lưới)
16. apple (táo)
17. orange (cam)
18. papaya (đu đủ)
19. coconut (dừa)
20. cotton (bông)
21. jute (đay)
22. coffee (cà phê)

### A.3. Cấu trúc merged_species.csv

| Cột | Mô tả |
|-----|-------|
| species | Tên khoa học |
| species_code | Mã số loài |
| Life.form | Dạng sống (herb, shrub, tree) |
| Habit | Thói quen sinh trưởng |
| Life.span | Chu kỳ sống |
| Physiology | Đặc điểm sinh lý |
| Category | Phân loại sử dụng |
| Plant.attributes | Thuộc tính cây |
| cycle_min | Chu kỳ tối thiểu (ngày) |
| cycle_max | Chu kỳ tối đa (ngày) |
| use.main | Mục đích sử dụng chính |
| use.detailed | Mục đích sử dụng chi tiết |
| use.part | Phần sử dụng |

## Phụ lục B: Code mẫu

### B.1. Training KNN Model

```python
import pandas as pd
import joblib
from sklearn.preprocessing import StandardScaler
from sklearn.neighbors import NearestNeighbors
from sklearn.model_selection import train_test_split
import numpy as np

# Load data
df = pd.read_csv('data/crop_env_features_no_abs.csv')
X = df.drop(columns=['species_label'])
y = df['species_label'].values

# Split data
X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, random_state=42, stratify=y
)

# Scale features
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

# Train KNN model
nn_model = NearestNeighbors(
    n_neighbors=5,
    metric='euclidean',
    algorithm='auto'
)
nn_model.fit(X_train_scaled)

# Save models
joblib.dump(scaler, 'model/scaler.joblib')
joblib.dump(nn_model, 'model/nn_model.joblib')
joblib.dump(y_train, 'model/labels.joblib')

print("KNN model training completed!")
```

### B.2. Training Random Forest Model

```python
import pandas as pd
import joblib
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.metrics import classification_report, accuracy_score

# Load data
df = pd.read_csv('data/Crop_recommendation.csv')
X = df.drop(columns=['label'])
y = df['label']

# Split data
X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, random_state=42, stratify=y
)

# Hyperparameter tuning
param_grid = {
    'n_estimators': [50, 100, 200],
    'max_depth': [5, 10, 15, None],
    'min_samples_split': [2, 5, 10],
    'min_samples_leaf': [1, 2, 4]
}

rf = RandomForestClassifier(random_state=42)
grid_search = GridSearchCV(
    rf, param_grid, cv=5, 
    scoring='accuracy', n_jobs=-1
)

# Train model
grid_search.fit(X_train, y_train)
best_model = grid_search.best_estimator_

# Evaluate
y_pred = best_model.predict(X_test)
accuracy = accuracy_score(y_test, y_pred)
print(f"Best parameters: {grid_search.best_params_}")
print(f"Test accuracy: {accuracy:.3f}")

# Save model
joblib.dump(best_model, 'model/random_forest_default_model.joblib')
print("Random Forest model training completed!")
```

### B.3. FastAPI Endpoint Example

```python
from fastapi import APIRouter, Depends, HTTPException
from typing import List
import joblib
import pandas as pd
import numpy as np

router = APIRouter()

class CropRequest(BaseModel):
    N: float
    P: float
    K: float
    temperature: float
    humidity: float
    ph: float
    rainfall: float

class CropResponse(BaseModel):
    name: str
    probability: float

# Load model
rf_model = joblib.load('model/random_forest_default_model.joblib')

@router.post('/suggest', response_model=List[CropResponse])
async def suggest_crops(request: CropRequest):
    try:
        # Prepare input data
        input_data = pd.DataFrame([request.dict()])
        
        # Make prediction
        probabilities = rf_model.predict_proba(input_data)[0]
        classes = rf_model.classes_
        
        # Get best prediction
        best_idx = np.argmax(probabilities)
        best_crop = classes[best_idx]
        best_prob = probabilities[best_idx]
        
        return [CropResponse(
            name=best_crop,
            probability=float(best_prob)
        )]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

### B.4. React Component Example

```jsx
import React, { useState } from 'react';
import axios from 'axios';

const CropSuggestionForm = () => {
  const [formData, setFormData] = useState({
    N: 50, P: 50, K: 50,
    temperature: 25, humidity: 70,
    ph: 6.5, rainfall: 100
  });
  
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      const response = await axios.post(
        'http://localhost:8001/rf/suggest',
        formData
      );
      setResults(response.data);
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: parseFloat(value)
    }));
  };

  return (
    <div className="max-w-md mx-auto bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-4">Crop Suggestion</h2>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        {Object.entries(formData).map(([key, value]) => (
          <div key={key}>
            <label className="block text-sm font-medium mb-1">
              {key.toUpperCase()}: {value}
            </label>
            <input
              type="range"
              name={key}
              min="0"
              max="200"
              value={value}
              onChange={handleInputChange}
              className="w-full"
            />
          </div>
        ))}
        
        <button
          type="submit"
          disabled={loading}
          className="w-full bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {loading ? 'Processing...' : 'Get Suggestion'}
        </button>
      </form>

      {results.length > 0 && (
        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-2">Recommended Crop:</h3>
          {results.map((crop, index) => (
            <div key={index} className="bg-green-50 p-3 rounded">
              <p className="font-medium">{crop.name}</p>
              <p className="text-sm text-gray-600">
                Confidence: {(crop.probability * 100).toFixed(1)}%
              </p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default CropSuggestionForm;
```

## Phụ lục C: Kết quả đánh giá mô hình

### C.1. Confusion Matrix - Random Forest

```
Actual\Predicted  rice  maize  cotton  coffee  ...
rice              45     2      0       0     ...
maize             1     38      1       0     ...
cotton            0      0     42       1     ...
coffee            0      0      0      41     ...
...               ...   ...    ...     ...   ...
```

### C.2. Classification Report - Random Forest

```
              precision    recall  f1-score   support

       apple       0.89      0.91      0.90        22
      banana       0.95      0.91      0.93        22
   blackgram       0.86      0.86      0.86        22
    chickpea       0.95      0.95      0.95        22
     coconut       1.00      1.00      1.00        22
      coffee       0.95      0.91      0.93        22
      cotton       0.86      0.91      0.88        22
      grapes       0.91      0.86      0.88        22
        jute       0.95      0.95      0.95        22
kidneybeans       0.86      0.82      0.84        22
      lentil       0.95      0.95      0.95        22
       maize       0.86      0.91      0.88        22
       mango       0.95      0.95      0.95        22
  mothbeans       0.91      0.86      0.88        22
    mungbean       0.86      0.91      0.88        22
  muskmelon       0.95      0.95      0.95        22
      orange       0.91      0.91      0.91        22
      papaya       1.00      0.95      0.98        22
  pigeonpeas       0.86      0.86      0.86        22
 pomegranate       1.00      1.00      1.00        22
        rice       0.91      0.91      0.91        22
   watermelon       0.95      1.00      0.98        22

    accuracy                           0.91       484
   macro avg       0.92      0.91      0.91       484
weighted avg       0.92      0.91      0.91       484
```

### C.3. Feature Importance - Random Forest

| Feature | Importance |
|---------|------------|
| rainfall | 0.234 |
| humidity | 0.187 |
| temperature | 0.156 |
| K | 0.143 |
| P | 0.128 |
| N | 0.089 |
| ph | 0.063 |

## Phụ lục D: Cấu hình hệ thống

### D.1. requirements.txt

```
fastapi==0.104.1
uvicorn==0.23.2
scikit-learn==1.3.0
pandas==2.0.3
numpy==1.24.3
joblib==1.3.1
pydantic==2.3.0
python-multipart==0.0.6
python-dotenv==1.0.0
```

### D.2. package.json

```json
{
  "name": "crop-suggestion-frontend",
  "version": "0.1.0",
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "axios": "^1.5.0",
    "tailwindcss": "^3.3.3",
    "react-toastify": "^11.0.5",
    "@radix-ui/react-checkbox": "^1.0.4",
    "@radix-ui/react-label": "^2.0.2"
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test",
    "eject": "react-scripts eject"
  }
}
```

### D.3. .env.example

```
# API Configuration
API_PREFIX=/api
KNN_API_PORT=8000
RF_API_PORT=8001
HOST=0.0.0.0
DEBUG=True

# Paths Configuration
MODEL_DIR=../model
DATA_DIR=../data
KNN_MODEL_PATH=../model/nn_model.joblib
RF_MODEL_PATH=../model/random_forest_default_model.joblib
SCALER_PATH=../model/scaler.joblib
LABELS_PATH=../model/labels.joblib

# CORS Configuration
CORS_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]

# Logging Configuration
LOG_LEVEL=INFO
```

## Phụ lục E: Hướng dẫn cài đặt và chạy

### E.1. Cài đặt Backend

```bash
# Tạo virtual environment
python -m venv .venv

# Kích hoạt virtual environment
# Windows:
.venv\Scripts\activate
# Linux/Mac:
source .venv/bin/activate

# Cài đặt dependencies
pip install -r requirements.txt

# Chạy KNN API server
uvicorn backend.main:app --reload --port 8000

# Chạy RF API server (terminal mới)
uvicorn backend.rf_api:app --reload --port 8001
```

### E.2. Cài đặt Frontend

```bash
# Di chuyển vào thư mục frontend
cd frontend

# Cài đặt dependencies
npm install

# Chạy development server
npm start
```

### E.3. Chạy toàn bộ hệ thống

```bash
# Windows
run.bat

# Linux/Mac
chmod +x run.sh
./run.sh
```

## Phụ lục F: API Documentation

### F.1. KNN API Endpoints

**POST /suggest**
- **Description:** Gợi ý top 5 cây trồng sử dụng KNN
- **Request Body:** CropRequest object
- **Response:** CropResponse với 5 suggestions
- **Status Codes:** 200 (Success), 422 (Validation Error), 500 (Server Error)

### F.2. Random Forest API Endpoints

**POST /rf/suggest**
- **Description:** Gợi ý cây trồng tối ưu sử dụng Random Forest
- **Request Body:** RFCropRequest object
- **Response:** RFCropResponse với 1 suggestion
- **Status Codes:** 200 (Success), 422 (Validation Error), 500 (Server Error)

**GET /rf/info**
- **Description:** Thông tin về mô hình Random Forest
- **Response:** Model information object
- **Status Codes:** 200 (Success)

---

**Lưu ý:** Phụ lục này cung cấp thông tin chi tiết bổ sung cho báo cáo chính. Tất cả code examples và configurations đều được test và hoạt động trong môi trường phát triển.
