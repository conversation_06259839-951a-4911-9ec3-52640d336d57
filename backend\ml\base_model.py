import joblib
from typing import Dict, Any, List, Optional
import pandas as pd
from ..core.logging import logger
from ..core.exceptions import ModelNotFoundException, PredictionException

class BaseModel:
    """
    Lớp cơ sở cho các mô hình học máy
    """
    def __init__(self, model_path: str):
        """
        Khởi tạo mô hình
        
        Args:
            model_path: Đường dẫn đến file model
        """
        self.model_path = model_path
        self.model = self._load_model()
    
    def _load_model(self):
        """
        Tải mô hình từ file
        
        Returns:
            Model đã tải
        
        Raises:
            ModelNotFoundException: <PERSON><PERSON><PERSON> không tìm thấy model
        """
        try:
            logger.info(f"Đang tải model từ {self.model_path}")
            model = joblib.load(self.model_path)
            logger.info(f"Đã tải model thành công")
            return model
        except Exception as e:
            logger.error(f"Lỗi khi tải model: {str(e)}")
            raise ModelNotFoundException(f"Kh<PERSON>ng thể tải model từ {self.model_path}: {str(e)}")
    
    def predict(self, features: Dict[str, Any]) -> List[Any]:
        """
        Dự đoán dựa trên đặc trưng đầu vào
        
        Args:
            features: Đặc trưng đầu vào
        
        Returns:
            Kết quả dự đoán
        
        Raises:
            PredictionException: Nếu có lỗi trong quá trình dự đoán
        """
        raise NotImplementedError("Các lớp con phải triển khai phương thức predict()")
