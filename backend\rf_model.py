import joblib
import pandas as pd
import os
import logging
from typing import List, Dict, Any, Optional
from pydantic import BaseModel

# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# L<PERSON>y đường dẫn tuyệt đối đến thư mục chứa model và data
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
MODEL_DIR = os.path.join(BASE_DIR, 'model')
DATA_DIR = os.path.join(BASE_DIR, 'data')

logger.info(f"Đang tải Random Forest model từ {MODEL_DIR} và data từ {DATA_DIR}")

# Định nghĩa các model Pydantic
class RFCropInfo(BaseModel):
    id: int
    name: str
    probability: float
    details: Optional[Dict[str, Any]] = None

class RFCropRequest(BaseModel):
    N: float  # Nitrogen content
    P: float  # Phosphorus content
    K: float  # Potassium content
    temperature: float  # Temperature in Celsius
    humidity: float  # Humidity percentage
    ph: float  # pH value
    rainfall: float  # Rainfall in mm

class RFCropResponse(BaseModel):
    suggestions: List[RFCropInfo]

    class Config:
        from_attributes = True

# Tải mô hình Random Forest và dữ liệu liên quan
try:
    # Tải mô hình Random Forest
    rf_model = joblib.load(os.path.join(MODEL_DIR, 'random_forest_default_model.joblib'))
    logger.info("Đã tải Random Forest model thành công")

    # Tải thông tin về các loại cây trồng (nếu có)
    try:
        crop_data = pd.read_csv(os.path.join(DATA_DIR, 'Crop_recommendation.csv'))
        logger.info("Đã tải thông tin cây trồng từ Crop_recommendation.csv thành công")
        # Lấy danh sách các loại cây trồng duy nhất
        unique_crops = crop_data['label'].unique()
        logger.info(f"Các loại cây trồng có sẵn: {unique_crops}")
    except Exception as e:
        logger.warning(f"Không thể tải thông tin cây trồng: {str(e)}")
        crop_data = None
        unique_crops = []

    # Tải thông tin chi tiết về cây trồng từ merged_species.csv nếu có
    try:
        species_data = pd.read_csv(os.path.join(DATA_DIR, 'merged_species.csv'))
        logger.info("Đã tải thông tin chi tiết về cây trồng từ merged_species.csv thành công")
    except Exception as e:
        logger.warning(f"Không thể tải thông tin chi tiết về cây trồng: {str(e)}")
        species_data = None

except Exception as e:
    logger.error(f"Lỗi khi tải Random Forest model và data: {str(e)}")
    raise

# Hàm để lấy thông tin chi tiết về cây trồng
def get_crop_details(crop_name: str) -> Dict[str, Any]:
    """
    Lấy thông tin chi tiết về cây trồng từ dữ liệu có sẵn.

    Args:
        crop_name: Tên của cây trồng

    Returns:
        Dict chứa thông tin chi tiết về cây trồng
    """
    details = {
        "name": crop_name,
        "description": f"Thông tin về {crop_name}"
    }

    # Nếu có dữ liệu chi tiết, thêm vào
    if species_data is not None:
        # Tìm kiếm trong species_data nếu có thông tin tương ứng
        # Đây là logic mẫu, cần điều chỉnh dựa trên cấu trúc thực tế của dữ liệu
        try:
            crop_info = species_data[species_data['species'].str.lower() == crop_name.lower()]
            if not crop_info.empty:
                row = crop_info.iloc[0]
                for col in crop_info.columns:
                    if col != 'species' and not pd.isna(row[col]):
                        details[col] = row[col]
        except Exception as e:
            logger.warning(f"Không thể lấy thông tin chi tiết cho {crop_name}: {str(e)}")

    return details

# Hàm dự đoán cây trồng sử dụng mô hình Random Forest
def predict_crop(input_data: Dict[str, float]) -> List[RFCropInfo]:
    """
    Dự đoán cây trồng phù hợp dựa trên dữ liệu đầu vào sử dụng mô hình Random Forest.

    Args:
        input_data: Dictionary chứa các đặc trưng đầu vào (N, P, K, temperature, humidity, ph, rainfall)

    Returns:
        Danh sách các cây trồng được gợi ý kèm xác suất
    """
    try:
        # Tạo DataFrame từ dữ liệu đầu vào
        input_df = pd.DataFrame([input_data])

        # Đảm bảo thứ tự các cột phù hợp với mô hình
        expected_columns = ['N', 'P', 'K', 'temperature', 'humidity', 'ph', 'rainfall']
        for col in expected_columns:
            if col not in input_df.columns:
                raise ValueError(f"Thiếu trường dữ liệu: {col}")

        # Sắp xếp lại các cột theo thứ tự mong đợi
        input_df = input_df[expected_columns]

        # Dự đoán xác suất cho mỗi lớp
        probabilities = rf_model.predict_proba(input_df)[0]

        # Lấy tên các lớp (cây trồng)
        class_names = rf_model.classes_

        # Tạo danh sách kết quả và sắp xếp theo xác suất giảm dần
        results = []
        for i, (crop_name, prob) in enumerate(zip(class_names, probabilities)):
            crop_id = i + 1  # ID tạm thời
            details = get_crop_details(crop_name)
            results.append(RFCropInfo(
                id=crop_id,
                name=crop_name,
                probability=float(prob),
                details=details
            ))

        # Sắp xếp kết quả theo xác suất giảm dần và chỉ lấy cây phù hợp nhất
        results.sort(key=lambda x: x.probability, reverse=True)
        best_result = results[:1]  # Chỉ lấy cây phù hợp nhất

        return best_result

    except Exception as e:
        logger.error(f"Lỗi khi dự đoán cây trồng: {str(e)}")
        raise
